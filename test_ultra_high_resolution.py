#!/usr/bin/env python3
"""
Ultra High Resolution Test Script

Script ini khusus untuk menguji apakah scraper dapat mengambil gambar
dengan resolusi tinggi seperti yang Anda lihat secara manual.

Fitur yang ditest:
1. URL conversion ke resolusi maksimal
2. Screenshot ultra high quality
3. Perbandingan ukuran file dengan manual screenshot
4. Verifikasi kualitas gambar
"""

import os
import sys
import time
from scraperBot import EBirdScraperBot
from PIL import Image
import requests

def test_ultra_high_resolution():
    """Test resolusi ultra tinggi"""
    print("🔬 TESTING ULTRA HIGH RESOLUTION CAPTURE")
    print("=" * 60)
    
    # Test URL - gunakan yang sama dengan yang Anda test manual
    test_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
    test_dir = os.path.join(os.getcwd(), "ultra_high_res_test")
    
    print(f"📍 Test URL: {test_url}")
    print(f"📁 Output directory: {test_dir}")
    print("🎯 Testing ULTRA HIGH RESOLUTION capture...")
    print("=" * 60)
    
    try:
        # Create output directory
        os.makedirs(test_dir, exist_ok=True)
        
        # Test dengan GUI mode agar bisa melihat prosesnya
        print("\n🖥️ Starting in GUI mode so you can see the process...")
        scraper = EBirdScraperBot(headless=False)
        
        print("\n📸 Testing ultra high-resolution capture...")
        print("⏱️ This will take a bit longer to ensure maximum quality...")
        
        start_time = time.time()
        result_count = scraper.scrape_ebird(
            ebird_url=test_url,
            output_dir=test_dir,
            max_images=3,  # Test dengan 3 gambar untuk perbandingan
            method='click_and_view',  # Method terbaik untuk resolusi tinggi
            timeout_minutes=15
        )
        
        test_time = time.time() - start_time
        
        print(f"\n✅ Ultra high-res test completed in {test_time:.1f} seconds")
        print(f"📊 Images processed: {result_count}")
        
        # Analyze hasil
        analyze_image_quality(test_dir)
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def analyze_image_quality(test_dir):
    """Analyze kualitas gambar yang dihasilkan"""
    print("\n🔍 ANALYZING IMAGE QUALITY")
    print("-" * 40)
    
    if not os.path.exists(test_dir):
        print("❌ Test directory not found")
        return
    
    image_files = [f for f in os.listdir(test_dir) if f.endswith(('.jpg', '.png'))]
    
    if not image_files:
        print("❌ No images found in test directory")
        return
    
    print(f"📁 Found {len(image_files)} images to analyze:")
    
    total_size = 0
    high_quality_count = 0
    
    for i, filename in enumerate(image_files, 1):
        filepath = os.path.join(test_dir, filename)
        
        try:
            # File size analysis
            file_size = os.path.getsize(filepath)
            file_size_kb = file_size / 1024
            total_size += file_size
            
            # Image dimension analysis
            try:
                with Image.open(filepath) as img:
                    width, height = img.size
                    megapixels = (width * height) / 1000000
                    
                    print(f"\n📸 {i}. {filename}")
                    print(f"   📏 Dimensions: {width} x {height} pixels ({megapixels:.1f} MP)")
                    print(f"   💾 File size: {file_size_kb:.1f} KB")
                    
                    # Quality assessment
                    quality_score = assess_image_quality(width, height, file_size_kb)
                    print(f"   ⭐ Quality score: {quality_score}/10")
                    
                    if quality_score >= 7:
                        high_quality_count += 1
                        print(f"   ✅ HIGH QUALITY IMAGE!")
                    elif quality_score >= 5:
                        print(f"   ⚠️ Medium quality")
                    else:
                        print(f"   ❌ Low quality - needs improvement")
                        
            except Exception as e:
                print(f"   ❌ Could not analyze image: {e}")
                
        except Exception as e:
            print(f"❌ Error analyzing {filename}: {e}")
    
    # Summary
    print(f"\n📊 QUALITY ANALYSIS SUMMARY")
    print("-" * 40)
    print(f"📁 Total images: {len(image_files)}")
    print(f"✅ High quality images: {high_quality_count}")
    print(f"💾 Total size: {total_size/1024:.1f} KB")
    print(f"📈 Average size: {(total_size/len(image_files))/1024:.1f} KB per image")
    
    if high_quality_count >= len(image_files) * 0.7:  # 70% high quality
        print(f"\n🎉 EXCELLENT! {high_quality_count}/{len(image_files)} images are high quality!")
        print("✅ Ultra high-resolution capture is working perfectly!")
    elif high_quality_count >= len(image_files) * 0.5:  # 50% high quality
        print(f"\n👍 GOOD! {high_quality_count}/{len(image_files)} images are high quality!")
        print("⚠️ Some improvements possible, but generally working well.")
    else:
        print(f"\n⚠️ NEEDS IMPROVEMENT! Only {high_quality_count}/{len(image_files)} images are high quality.")
        print("🔧 The ultra high-resolution system needs further optimization.")

def assess_image_quality(width, height, file_size_kb):
    """Assess image quality based on dimensions and file size"""
    score = 0
    
    # Dimension score (out of 5)
    megapixels = (width * height) / 1000000
    if megapixels >= 3.0:  # 3+ megapixels
        score += 5
    elif megapixels >= 2.0:  # 2+ megapixels
        score += 4
    elif megapixels >= 1.0:  # 1+ megapixels
        score += 3
    elif megapixels >= 0.5:  # 0.5+ megapixels
        score += 2
    else:
        score += 1
    
    # File size score (out of 5)
    if file_size_kb >= 500:  # 500+ KB indicates high quality
        score += 5
    elif file_size_kb >= 200:  # 200+ KB is good
        score += 4
    elif file_size_kb >= 100:  # 100+ KB is acceptable
        score += 3
    elif file_size_kb >= 50:   # 50+ KB is low
        score += 2
    else:
        score += 1
    
    return score

def compare_with_manual_screenshot():
    """Instruksi untuk membandingkan dengan screenshot manual"""
    print("\n📋 MANUAL COMPARISON INSTRUCTIONS")
    print("=" * 60)
    print("Untuk memverifikasi bahwa scraper menghasilkan kualitas yang sama")
    print("dengan screenshot manual Anda:")
    print()
    print("1. 🌐 Buka URL yang sama di browser:")
    print("   https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1")
    print()
    print("2. 🖱️ Klik pada gambar yang sama seperti yang di-scrape")
    print()
    print("3. 📸 Ambil screenshot manual (seperti yang Anda lakukan sebelumnya)")
    print()
    print("4. 📊 Bandingkan:")
    print("   - Ukuran file (KB)")
    print("   - Dimensi gambar (pixel)")
    print("   - Kualitas visual")
    print()
    print("5. ✅ Jika hasilnya sama atau lebih baik, scraper berhasil!")
    print("   ⚠️ Jika masih kurang, laporkan untuk perbaikan lebih lanjut.")

def main():
    print("🚀 ULTRA HIGH RESOLUTION TEST SUITE")
    print("=" * 60)
    print("Script ini akan menguji apakah scraper dapat menghasilkan")
    print("gambar dengan resolusi tinggi seperti screenshot manual Anda.")
    print()
    
    # Run the test
    success = test_ultra_high_resolution()
    
    # Show comparison instructions
    compare_with_manual_screenshot()
    
    if success:
        print("\n🎊 ULTRA HIGH RESOLUTION TEST COMPLETED!")
        print("Periksa folder 'ultra_high_res_test' untuk melihat hasilnya.")
        print("Bandingkan dengan screenshot manual Anda untuk verifikasi.")
    else:
        print("\n⚠️ Test encountered issues.")
        print("Periksa error messages di atas untuk troubleshooting.")

if __name__ == "__main__":
    main()
