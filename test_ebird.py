#!/usr/bin/env python3
"""
Test script untuk eBird Scraper Bot
Script ini akan menguji apakah bot dapat mengakses dan mengekstrak gambar dari eBird
"""

import os
import sys
from scraperBot import EBirdScraperBot

def test_basic_functionality():
    """Test fungsi dasar eBird scraper"""
    print("=== Testing eBird Scraper Bot ===\n")
    
    # URL test untuk Javan Munia di Indonesia
    test_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
    test_dir = os.path.join(os.getcwd(), "test_ebird_output")
    
    print(f"Test URL: {test_url}")
    print(f"Output directory: {test_dir}")
    print(f"Max images: 3 (for testing)")
    
    try:
        # Buat scraper dengan headless mode untuk testing
        print("\n1. Initializing eBird scraper...")
        scraper = EBirdScraperBot(headless=True)
        print("✓ Scraper initialized successfully")
        
        # Test scraping dengan jumlah gambar kecil
        print("\n2. Starting scraping test...")
        downloaded_count = scraper.scrape_ebird(
            ebird_url=test_url,
            output_dir=test_dir,
            max_images=3  # Hanya 3 gambar untuk test
        )
        
        print(f"\n3. Test completed!")
        print(f"Downloaded: {downloaded_count} images")
        
        # Cek hasil
        if downloaded_count > 0:
            print("✓ Test PASSED - Images downloaded successfully")
            
            # List files yang didownload
            if os.path.exists(test_dir):
                files = [f for f in os.listdir(test_dir) if f.endswith('.jpg')]
                print(f"Files downloaded: {files}")
            
            return True
        else:
            print("✗ Test FAILED - No images downloaded")
            return False
            
    except Exception as e:
        print(f"✗ Test FAILED with error: {e}")
        return False

def test_url_extraction():
    """Test ekstraksi URL tanpa download"""
    print("\n=== Testing URL Extraction ===\n")
    
    test_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
    
    try:
        print("1. Initializing scraper...")
        scraper = EBirdScraperBot(headless=True)
        
        print("2. Opening eBird page...")
        scraper.wd.get(test_url)
        
        print("3. Extracting image URLs...")
        image_urls = scraper._get_image_urls_from_page()
        
        print(f"4. Found {len(image_urls)} image URLs")
        
        if len(image_urls) > 0:
            print("✓ URL extraction test PASSED")
            print("Sample URLs:")
            for i, url in enumerate(image_urls[:3]):  # Show first 3 URLs
                print(f"  {i+1}. {url}")
            
            scraper.wd.quit()
            return True
        else:
            print("✗ URL extraction test FAILED - No URLs found")
            scraper.wd.quit()
            return False
            
    except Exception as e:
        print(f"✗ URL extraction test FAILED: {e}")
        try:
            scraper.wd.quit()
        except:
            pass
        return False

def main():
    print("eBird Scraper Bot Test Suite")
    print("=" * 40)
    
    # Test 1: URL extraction
    url_test_passed = test_url_extraction()
    
    # Test 2: Full functionality (hanya jika URL extraction berhasil)
    if url_test_passed:
        full_test_passed = test_basic_functionality()
    else:
        print("\nSkipping full test due to URL extraction failure")
        full_test_passed = False
    
    # Summary
    print("\n" + "=" * 40)
    print("TEST SUMMARY:")
    print(f"URL Extraction: {'PASSED' if url_test_passed else 'FAILED'}")
    print(f"Full Download:  {'PASSED' if full_test_passed else 'FAILED'}")
    
    if url_test_passed and full_test_passed:
        print("\n✓ All tests PASSED! eBird scraper is working correctly.")
        print("\nYou can now use:")
        print("python scraperBot.py --mode ebird --ebird_url 'YOUR_EBIRD_URL' --max_images 10")
    else:
        print("\n✗ Some tests FAILED. Please check the errors above.")
        print("\nTroubleshooting tips:")
        print("1. Make sure you have internet connection")
        print("2. Check if ChromeDriver is properly installed")
        print("3. Try running with --headless=False to see what's happening")

if __name__ == "__main__":
    main()
