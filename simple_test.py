#!/usr/bin/env python3
"""
Simple test untuk memeriksa apakah dependencies berfungsi
"""

def test_imports():
    """Test import semua dependencies"""
    try:
        print("Testing selenium import...")
        import selenium
        print(f"✓ Selenium version: {selenium.__version__}")
        
        print("Testing webdriver import...")
        from selenium import webdriver
        print("✓ Webdriver import successful")
        
        print("Testing webdriver-manager import...")
        from webdriver_manager.chrome import ChromeDriverManager
        print("✓ WebDriver Manager import successful")
        
        print("Testing other dependencies...")
        import requests
        import tqdm
        print("✓ All dependencies imported successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Import error: {e}")
        return False

def test_chromedriver():
    """Test ChromeDriver installation"""
    try:
        print("\nTesting ChromeDriver installation...")
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        from selenium import webdriver
        
        # Setup Chrome options
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        # Install and setup ChromeDriver
        print("Installing ChromeDriver...")
        service = Service(ChromeDriverManager().install())
        
        print("Starting Chrome browser...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        print("Testing basic navigation...")
        driver.get("https://www.google.com")
        title = driver.title
        print(f"✓ Successfully opened Google, title: {title}")
        
        driver.quit()
        print("✓ ChromeDriver test successful")
        
        return True
        
    except Exception as e:
        print(f"✗ ChromeDriver test failed: {e}")
        return False

def main():
    print("=== Simple Test for eBird Scraper Dependencies ===\n")
    
    # Test 1: Imports
    import_success = test_imports()
    
    # Test 2: ChromeDriver (only if imports successful)
    if import_success:
        chromedriver_success = test_chromedriver()
    else:
        chromedriver_success = False
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY:")
    print(f"Dependencies: {'PASSED' if import_success else 'FAILED'}")
    print(f"ChromeDriver:  {'PASSED' if chromedriver_success else 'FAILED'}")
    
    if import_success and chromedriver_success:
        print("\n✓ All tests PASSED! You can now use the eBird scraper.")
        print("\nNext steps:")
        print("1. Run: python scraperBot.py --mode ebird --ebird_url 'YOUR_URL' --max_images 5")
        print("2. Or run: python ebird_example.py")
    else:
        print("\n✗ Some tests FAILED. Please fix the issues above.")
        if not import_success:
            print("- Try: pip install selenium requests tqdm webdriver-manager")
        if not chromedriver_success:
            print("- Make sure Chrome browser is installed")
            print("- Check your internet connection")

if __name__ == "__main__":
    main()
