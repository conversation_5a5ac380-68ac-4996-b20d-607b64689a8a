#!/usr/bin/env python3
"""
Test script untuk fitur Click and View eBird Scraper
Script ini akan menguji fitur baru klik gambar, lihat versi penuh, kemudian download
"""

import os
import sys
from scraperBot import EBirdScraperBot

def test_click_and_view():
    """Test fitur click and view"""
    print("=== Testing Click and View Feature ===\n")
    
    # URL test untuk Javan Munia di Indonesia
    test_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
    test_dir = os.path.join(os.getcwd(), "test_click_view")
    
    print(f"Test URL: {test_url}")
    print(f"Output directory: {test_dir}")
    print(f"Method: click_and_view")
    print(f"Max images: 3 (for testing)")
    
    try:
        # Buat scraper dengan GUI untuk melihat proses
        print("\n1. Initializing eBird scraper (with G<PERSON> to see the process)...")
        scraper = EBirdScraperBot(headless=False)  # GUI mode untuk melihat proses
        print("✓ Scraper initialized successfully")
        
        # Test scraping dengan fitur click and view
        print("\n2. Starting click and view test...")
        print("   - Bot akan klik setiap gambar")
        print("   - Melihat versi penuh")
        print("   - Download atau screenshot")
        
        downloaded_count = scraper.scrape_ebird(
            ebird_url=test_url,
            output_dir=test_dir,
            max_images=3,  # Hanya 3 gambar untuk test
            method='click_and_view'  # Fitur baru
        )
        
        print(f"\n3. Test completed!")
        print(f"Downloaded/captured: {downloaded_count} items")
        
        # Cek hasil
        if downloaded_count > 0:
            print("✓ Test PASSED - Items downloaded/captured successfully")
            
            # List files yang didownload/screenshot
            if os.path.exists(test_dir):
                files = [f for f in os.listdir(test_dir) if f.endswith(('.jpg', '.png'))]
                print(f"Files created: {files}")
                
                # Analisis jenis file
                jpg_files = [f for f in files if f.endswith('.jpg')]
                png_files = [f for f in files if f.endswith('.png')]
                
                print(f"Downloaded images (JPG): {len(jpg_files)}")
                print(f"Screenshots (PNG): {len(png_files)}")
            
            return True
        else:
            print("✗ Test FAILED - No items downloaded/captured")
            return False
            
    except Exception as e:
        print(f"✗ Test FAILED with error: {e}")
        return False

def test_comparison():
    """Test perbandingan antara method lama dan baru"""
    print("\n=== Comparison Test: Old vs New Method ===\n")
    
    test_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
    
    results = {}
    
    # Test method baru
    print("Testing NEW method (click_and_view)...")
    try:
        scraper_new = EBirdScraperBot(headless=True)
        count_new = scraper_new.scrape_ebird(
            ebird_url=test_url,
            output_dir=os.path.join(os.getcwd(), "test_new_method"),
            max_images=2,
            method='click_and_view'
        )
        results['new'] = count_new
        print(f"✓ New method: {count_new} items")
    except Exception as e:
        print(f"✗ New method failed: {e}")
        results['new'] = 0
    
    # Test method lama
    print("\nTesting OLD method (direct_url)...")
    try:
        scraper_old = EBirdScraperBot(headless=True)
        count_old = scraper_old.scrape_ebird(
            ebird_url=test_url,
            output_dir=os.path.join(os.getcwd(), "test_old_method"),
            max_images=2,
            method='direct_url'
        )
        results['old'] = count_old
        print(f"✓ Old method: {count_old} items")
    except Exception as e:
        print(f"✗ Old method failed: {e}")
        results['old'] = 0
    
    # Hasil perbandingan
    print(f"\n=== COMPARISON RESULTS ===")
    print(f"New method (click_and_view): {results.get('new', 0)} items")
    print(f"Old method (direct_url):     {results.get('old', 0)} items")
    
    if results.get('new', 0) > 0 or results.get('old', 0) > 0:
        print("✓ At least one method works!")
        return True
    else:
        print("✗ Both methods failed")
        return False

def main():
    print("eBird Click and View Feature Test")
    print("=" * 50)
    
    # Test 1: Click and view feature
    click_test_passed = test_click_and_view()
    
    # Test 2: Comparison
    comparison_test_passed = test_comparison()
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY:")
    print(f"Click & View Test: {'PASSED' if click_test_passed else 'FAILED'}")
    print(f"Comparison Test:   {'PASSED' if comparison_test_passed else 'FAILED'}")
    
    if click_test_passed:
        print("\n✓ Click and View feature is working!")
        print("\nYou can now use:")
        print("python scraperBot.py --mode ebird --ebird_url 'YOUR_URL' --method click_and_view --max_images 10")
        print("\nOr for faster (but lower quality):")
        print("python scraperBot.py --mode ebird --ebird_url 'YOUR_URL' --method direct_url --max_images 10")
    else:
        print("\n✗ Click and View feature needs debugging.")
        print("Try using the old method: --method direct_url")

if __name__ == "__main__":
    main()
