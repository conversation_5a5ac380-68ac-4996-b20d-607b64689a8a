# 🐦 Panduan Penggunaan eBird Photo Scraper Bot

Bot ScraperBot Anda telah berhasil dimodifikasi untuk mendownload foto burung dari eBird.org! 

## ✅ Status: SIAP DIGUNAKAN

Bot telah ditest dan berhasil mendownload foto Javan Munia dari Indonesia.

## 🚀 Cara Penggunaan Cepat

### 🆕 1. Method Baru: Klik dan <PERSON> (RECOMMENDED)
```bash
# Bot akan klik setiap gambar, melihat versi penuh, kemudian download
python scraperBot.py --mode ebird --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" --method click_and_view --max_images 20
```

### 2. Method Lama: URL Langsung (Lebih Cepat)
```bash
# Ambil URL gambar langsung tanpa klik (method lama)
python scraperBot.py --mode ebird --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" --method direct_url --max_images 20
```

### 3. Mode Headless (Tanpa Browser Window)
```bash
python scraperBot.py --mode ebird --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" --method click_and_view --max_images 15 --headless
```

### 4. Download ke Folder Khusus
```bash
python scraperBot.py --mode ebird --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" --method click_and_view --max_images 10 --out_directory "./foto_burung"
```

## 📋 Contoh URL eBird Lainnya

### Berdasarkan Spesies dan Lokasi
- **Javan Munia di Indonesia**: `https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1`
- **White-rumped Shama di Indonesia**: `https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=whirsh1`
- **Oriental Magpie-Robin di Indonesia**: `https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=ormaro`

### Berdasarkan Lokasi Saja
- **Semua burung di Indonesia**: `https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID`
- **Semua burung di Jawa**: `https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID-JA`
- **Semua burung di Bali**: `https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID-BA`

## 🎯 Keunggulan Bot Ini

✅ **Download Foto Asli** - Bukan hanya metadata, tapi file gambar sesungguhnya
✅ **Resolusi Tinggi** - Otomatis mencari gambar dengan kualitas terbaik
✅ **🆕 Klik dan Lihat** - Bot klik gambar dulu, lihat versi penuh, baru download
✅ **Spesifik Spesies** - Foto burung yang akurat sesuai spesies yang dipilih
✅ **Filter Lokasi** - Bisa memilih foto dari wilayah tertentu
✅ **Screenshot Backup** - Jika tidak bisa download, otomatis screenshot
✅ **Rate Limiting** - Menghormati server eBird dengan jeda yang tepat
✅ **Auto ChromeDriver** - Otomatis download dan setup ChromeDriver

## 🔄 Perbedaan Method Scraping

### 🆕 Method "click_and_view" (RECOMMENDED)
**Cara kerja:**
1. Bot mencari semua gambar yang bisa diklik
2. Klik setiap gambar untuk membuka versi penuh
3. Ambil URL gambar resolusi tinggi atau screenshot
4. Download file gambar atau simpan screenshot

**Keunggulan:**
- ✅ Kualitas gambar maksimal (resolusi penuh)
- ✅ Mirip dengan "klik kanan save as" manual
- ✅ Bisa screenshot jika URL tidak tersedia
- ✅ Hasil lebih konsisten

**Kekurangan:**
- ⏱️ Sedikit lebih lambat (karena harus klik satu-satu)
- 🖥️ Membutuhkan browser GUI untuk hasil optimal

### Method "direct_url" (Method Lama)
**Cara kerja:**
1. Scan halaman untuk mencari URL gambar langsung
2. Konversi ke resolusi tinggi jika memungkinkan
3. Download langsung dari URL

**Keunggulan:**
- ⚡ Lebih cepat
- 🤖 Bisa jalan full headless
- 💾 Lebih ringan resource

**Kekurangan:**
- 📉 Mungkin tidak mendapat resolusi maksimal
- 🎯 Tergantung struktur HTML halaman

## 🛠️ Parameter Lengkap

```bash
python scraperBot.py --mode ebird [OPTIONS]
```

**Wajib:**
- `--mode ebird` - Aktifkan mode eBird
- `--ebird_url "URL"` - URL halaman eBird yang ingin di-scrape

**Opsional:**
- `--method METHOD` - Metode scraping:
  - `click_and_view` (default) - Klik gambar, lihat versi penuh, download
  - `direct_url` - Ambil URL langsung (method lama)
- `--max_images N` - Maksimal jumlah gambar (default: 50)
- `--out_directory "PATH"` - Direktori output (default: direktori saat ini)
- `--headless` - Jalankan browser tanpa GUI (untuk background/server)

**Contoh Lengkap:**
```bash
python scraperBot.py --mode ebird --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" --method click_and_view --max_images 15 --out_directory "./burung_jawa" --headless
```

## 📁 Hasil Download

Gambar akan disimpan dengan format:
- `ebird_image_001.jpg`
- `ebird_image_002.jpg`
- `ebird_image_003.jpg`
- dst...

## 🔧 Troubleshooting

### Error "ChromeDriver not found"
```bash
python simple_test.py
```
Script ini akan otomatis download ChromeDriver yang sesuai.

### Error "No images found"
- Pastikan URL eBird valid dan memiliki foto
- Coba kurangi `--max_images` 
- Periksa koneksi internet

### Error "Permission denied"
- Pastikan direktori output dapat ditulis
- Coba jalankan sebagai administrator

## 🎮 Script Bantuan

1. **Test Dependencies**: `python simple_test.py`
2. **Contoh Penggunaan**: `python ebird_example.py`
3. **Setup Otomatis**: `python setup_ebird.py`
4. **Windows Batch**: `run_ebird_scraper.bat`

## 📝 Catatan Penting

- Bot menghormati robots.txt dan rate limiting eBird
- Gunakan dengan bijak dan tidak berlebihan
- Foto tetap memiliki copyright dari fotografer asli
- Pastikan penggunaan sesuai terms of service eBird.org

## 🎉 Selamat!

Bot Anda sekarang dapat mendownload foto burung berkualitas tinggi dari eBird.org seperti yang Anda inginkan - mirip dengan "klik kanan save as" tapi otomatis untuk banyak gambar sekaligus!
