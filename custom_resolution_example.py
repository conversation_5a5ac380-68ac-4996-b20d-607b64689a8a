#!/usr/bin/env python3
"""
Custom Resolution Example

Script ini menunjukkan cara mengatur resolusi custom untuk mendapatkan
gambar dengan ukuran dan kualitas yang Anda inginkan.
"""

import os
from scraperBot import EBirdScraperBot

def test_different_resolutions():
    """Test berbagai resolusi untuk melihat perbedaannya"""
    
    test_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
    base_output_dir = "resolution_comparison"
    
    # Berbagai resolusi untuk ditest
    resolutions = [
        ("HD", (1920, 1080)),
        ("2K", (2560, 1440)), 
        ("4K", (3840, 2160)),
        ("Custom_High", (3200, 2400))
    ]
    
    print("🔬 TESTING DIFFERENT RESOLUTIONS")
    print("=" * 50)
    
    for name, (width, height) in resolutions:
        print(f"\n📐 Testing {name} resolution: {width}x{height}")
        
        output_dir = os.path.join(base_output_dir, name)
        os.makedirs(output_dir, exist_ok=True)
        
        try:
            # Create scraper with custom resolution
            scraper = EBirdScraperBot(
                headless=True,  # Headless untuk speed
                custom_resolution=(width, height)
            )
            
            # Set ultra quality mode
            scraper.ultra_quality_mode = True
            
            # Test dengan 2 gambar per resolusi
            result = scraper.scrape_ebird(
                ebird_url=test_url,
                output_dir=output_dir,
                max_images=2,
                method='click_and_view',
                timeout_minutes=10
            )
            
            print(f"✅ {name} completed: {result} images")
            
            # Analyze hasil
            analyze_resolution_results(output_dir, name)
            
        except Exception as e:
            print(f"❌ {name} failed: {e}")

def analyze_resolution_results(output_dir, resolution_name):
    """Analyze hasil dari setiap resolusi"""
    
    if not os.path.exists(output_dir):
        return
        
    files = [f for f in os.listdir(output_dir) if f.endswith(('.jpg', '.png'))]
    
    if not files:
        print(f"   ⚠️ No files found for {resolution_name}")
        return
    
    total_size = 0
    for filename in files:
        filepath = os.path.join(output_dir, filename)
        file_size = os.path.getsize(filepath)
        total_size += file_size
        
        # Try to get image dimensions
        try:
            from PIL import Image
            with Image.open(filepath) as img:
                width, height = img.size
                megapixels = (width * height) / 1000000
                print(f"   📸 {filename}: {width}x{height} ({megapixels:.1f}MP, {file_size/1024:.1f}KB)")
        except:
            print(f"   📸 {filename}: {file_size/1024:.1f}KB")
    
    avg_size = total_size / len(files) if files else 0
    print(f"   📊 Average size: {avg_size/1024:.1f}KB")

def custom_resolution_scraper_example():
    """Contoh penggunaan scraper dengan resolusi custom"""
    
    print("\n🎯 CUSTOM RESOLUTION SCRAPER EXAMPLE")
    print("=" * 50)
    
    # Resolusi custom yang sangat tinggi
    custom_width = 4000
    custom_height = 3000
    
    print(f"📐 Using custom resolution: {custom_width}x{custom_height}")
    print("🎯 This should produce very high quality images!")
    
    # Create scraper dengan resolusi custom
    scraper = EBirdScraperBot(
        headless=False,  # GUI mode untuk melihat proses
        custom_resolution=(custom_width, custom_height)
    )
    
    # Aktifkan ultra quality mode
    scraper.ultra_quality_mode = True
    
    test_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
    output_dir = "custom_ultra_high_res"
    
    try:
        result = scraper.scrape_ebird(
            ebird_url=test_url,
            output_dir=output_dir,
            max_images=3,
            method='click_and_view',
            timeout_minutes=15
        )
        
        print(f"\n✅ Custom resolution scraping completed!")
        print(f"📊 Images processed: {result}")
        print(f"📁 Check folder: {output_dir}")
        
        # Analyze hasil
        analyze_resolution_results(output_dir, "Custom Ultra High")
        
    except Exception as e:
        print(f"❌ Custom resolution scraping failed: {e}")

def show_resolution_settings_locations():
    """Tunjukkan di mana pengaturan resolusi berada dalam kode"""
    
    print("\n📍 LOKASI PENGATURAN RESOLUSI DALAM KODE")
    print("=" * 50)
    
    locations = [
        {
            "file": "scraperBot.py",
            "line": "135-138",
            "description": "Constructor dengan custom_resolution parameter",
            "code": "def __init__(self, headless=False, custom_resolution=None):"
        },
        {
            "file": "scraperBot.py", 
            "line": "147-150",
            "description": "Browser window size setting",
            "code": "chrome_options.add_argument(f'--window-size={width},{height}')"
        },
        {
            "file": "scraperBot.py",
            "line": "580-589", 
            "description": "URL resolution parameters",
            "code": "ultra_params = ['?size=original&quality=100', ...]"
        },
        {
            "file": "scraperBot.py",
            "line": "151-155",
            "description": "High DPI and scale factor settings", 
            "code": "chrome_options.add_argument('--force-device-scale-factor=1')"
        }
    ]
    
    for i, loc in enumerate(locations, 1):
        print(f"\n{i}. {loc['description']}")
        print(f"   📁 File: {loc['file']}")
        print(f"   📍 Line: {loc['line']}")
        print(f"   💻 Code: {loc['code']}")

def main():
    print("🚀 CUSTOM RESOLUTION CONFIGURATION GUIDE")
    print("=" * 60)
    
    # Show where resolution settings are located
    show_resolution_settings_locations()
    
    print("\n" + "=" * 60)
    print("Choose an option:")
    print("1. Test different resolutions (comparison)")
    print("2. Run custom ultra-high resolution example")
    print("3. Show settings locations only")
    
    choice = input("\nEnter choice (1-3): ").strip()
    
    if choice == "1":
        test_different_resolutions()
    elif choice == "2":
        custom_resolution_scraper_example()
    elif choice == "3":
        pass  # Already shown above
    else:
        print("Invalid choice. Showing settings locations only.")
    
    print("\n🎊 Custom resolution guide completed!")
    print("You can now modify the resolution settings in scraperBot.py")

if __name__ == "__main__":
    main()
