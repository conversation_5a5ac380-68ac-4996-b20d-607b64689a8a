#!/usr/bin/env python3
"""
Contoh penggunaan eBird Scraper Bot

Script ini menunjukkan cara menggunakan EBirdScraperBot untuk mendownload
foto burung dari eBird.org

Contoh URL eBird:
- Javan <PERSON> di Indonesia: https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1
- Semua burung di Indonesia: https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID
- Burung tertentu di seluruh dunia: https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&taxonCode=javmun1
"""

from scraperBot import EBirdScraperBot
import os

def main():
    # Contoh URL untuk Javan Munia di Indonesia
    ebird_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"

    # Direktori output
    output_dir = os.path.join(os.getcwd(), "ebird_photos", "javan_munia")

    print("=== eBird Scraper dengan Fitur Klik dan Lihat ===\n")

    # Contoh 1: Method baru - klik gambar dulu, lihat versi penuh
    print("1. Menggunakan method 'click_and_view' (RECOMMENDED)")
    print("   - Klik setiap gambar untuk melihat versi penuh")
    print("   - Download gambar resolusi tinggi atau screenshot")
    print("   - Hasil lebih baik tapi sedikit lebih lambat\n")

    scraper1 = EBirdScraperBot(headless=False)  # Set False untuk melihat proses

    try:
        downloaded_count1 = scraper1.scrape_ebird(
            ebird_url=ebird_url,
            output_dir=os.path.join(output_dir, "click_method"),
            max_images=5,  # Test dengan 5 gambar dulu
            method='click_and_view'  # Method baru
        )

        print(f"Method 1 selesai! Downloaded {downloaded_count1} items\n")

    except Exception as e:
        print(f"Error method 1: {e}")
        try:
            scraper1.wd.quit()
        except:
            pass

    # Contoh 2: Method lama - ambil URL langsung
    print("2. Menggunakan method 'direct_url' (method lama)")
    print("   - Ambil URL gambar langsung tanpa klik")
    print("   - Lebih cepat tapi mungkin resolusi tidak maksimal\n")

    scraper2 = EBirdScraperBot(headless=True)  # Headless untuk method lama

    try:
        downloaded_count2 = scraper2.scrape_ebird(
            ebird_url=ebird_url,
            output_dir=os.path.join(output_dir, "direct_method"),
            max_images=5,  # Test dengan 5 gambar
            method='direct_url'  # Method lama
        )

        print(f"Method 2 selesai! Downloaded {downloaded_count2} items\n")

    except Exception as e:
        print(f"Error method 2: {e}")
        try:
            scraper2.wd.quit()
        except:
            pass

    print("=== Perbandingan Hasil ===")
    print(f"Click & View method: {downloaded_count1 if 'downloaded_count1' in locals() else 0} items")
    print(f"Direct URL method:   {downloaded_count2 if 'downloaded_count2' in locals() else 0} items")
    print(f"\nFile tersimpan di: {output_dir}")
    print("\nRekomendasi: Gunakan 'click_and_view' untuk hasil terbaik!")

if __name__ == "__main__":
    main()
