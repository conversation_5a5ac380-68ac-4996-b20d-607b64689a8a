# eBird Photo Scraper Bot

Bot ini telah dimodifikasi untuk dapat mendownload foto burung dari eBird.org selain dari Google Images.

## Fitur Baru

- **Mode eBird**: Download foto burung langsung dari eBird.org
- **Download Resolusi Tinggi**: Otomatis mencari dan mendownload gambar dengan resolusi terbaik
- **Metadata Preservation**: Mempertahankan kualitas gambar asli
- **Smart Scrolling**: Otomatis scroll dan load lebih banyak gambar
- **Rate Limiting**: Menghindari pembatasan server dengan jeda yang tepat

## Instalasi

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Download ChromeDriver:
   - Download dari https://chromedriver.chromium.org/
   - Letakkan `chromedriver.exe` di direktori yang sama dengan script
   - Atau install via webdriver-manager (otomatis)

## Penggunaan

### Mode eBird (Baru)

```bash
# Download foto Javan Munia dari Indonesia
python scraperBot.py --mode ebird --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" --max_images 50

# Download dengan mode headless (tanpa GUI browser)
python scraperBot.py --mode ebird --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" --headless --max_images 30

# Specify output directory
python scraperBot.py --mode ebird --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" --out_directory "./bird_photos" --max_images 25
```

### Mode Google Images (Lama)

```bash
# Download dari Google Images (mode lama)
python scraperBot.py --mode google --search "burung murai" --min_image_count 20
```

## Contoh URL eBird

### Berdasarkan Spesies dan Lokasi
- Javan Munia di Indonesia: `https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1`
- White-rumped Shama di Indonesia: `https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=whirsh1`

### Berdasarkan Lokasi Saja
- Semua burung di Indonesia: `https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID`
- Semua burung di Jawa: `https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID-JA`

### Berdasarkan Spesies Saja
- Javan Munia di seluruh dunia: `https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&taxonCode=javmun1`

## Parameter

### Mode eBird
- `--mode ebird`: Aktifkan mode eBird
- `--ebird_url`: URL halaman eBird yang ingin di-scrape (wajib)
- `--max_images`: Maksimal jumlah gambar (default: 50)
- `--out_directory`: Direktori output (default: direktori saat ini)
- `--headless`: Jalankan browser tanpa GUI

### Mode Google Images
- `--mode google`: Aktifkan mode Google Images
- `--search`: Kata kunci pencarian (wajib)
- `--min_image_count`: Minimal jumlah gambar (wajib)
- `--out_directory`: Direktori output

## Contoh Penggunaan Programmatik

```python
from scraperBot import EBirdScraperBot

# Buat scraper
scraper = EBirdScraperBot(headless=False)

# Download foto
downloaded_count = scraper.scrape_ebird(
    ebird_url="https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1",
    output_dir="./bird_photos",
    max_images=30
)

print(f"Downloaded {downloaded_count} photos")
```

## Tips

1. **Gunakan URL yang Spesifik**: Semakin spesifik URL eBird, semakin relevan foto yang didownload
2. **Batasi Jumlah Gambar**: Gunakan `--max_images` untuk menghindari download berlebihan
3. **Mode Headless**: Gunakan `--headless` untuk menjalankan di server atau background
4. **Jeda Antar Request**: Bot sudah mengatur jeda otomatis untuk menghindari rate limiting

## Troubleshooting

1. **ChromeDriver Error**: Pastikan ChromeDriver compatible dengan versi Chrome Anda
2. **Timeout Error**: Coba kurangi `--max_images` atau periksa koneksi internet
3. **Permission Error**: Pastikan direktori output dapat ditulis
4. **Empty Results**: Periksa URL eBird apakah valid dan memiliki foto

## Catatan Penting

- Bot ini menghormati robots.txt dan rate limiting
- Gunakan dengan bijak dan tidak berlebihan
- Pastikan penggunaan sesuai dengan terms of service eBird.org
- Foto yang didownload tetap memiliki copyright dari fotografer asli
