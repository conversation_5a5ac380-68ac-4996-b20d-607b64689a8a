# 📖 Complete eBird Scraper User Guide

## 🚀 Quick Start

### **Minimum Command (Recommended for Beginners)**
```bash
python scraperBot.py --mode ebird --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
```

### **Full Command with All Options**
```bash
python scraperBot.py --mode ebird \
  --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" \
  --method click_and_view \
  --ultra_quality \
  --max_images 50 \
  --timeout 60 \
  --out_directory "./my_bird_photos" \
  --headless
```

## 📋 Complete Parameter Guide

### **Required Parameters**
```bash
--mode ebird                    # WAJIB: Set mode ke eBird
--ebird_url "URL_EBIRD"        # WAJIB: URL catalog eBird
```

### **Optional Parameters**
```bash
--max_images 50                # Maksimal gambar (default: 50)
--method click_and_view        # Method scraping (default: click_and_view)
--out_directory "./photos"     # Folder output (default: folder saat ini)
--timeout 60                   # Timeout dalam menit (default: 30)
--ultra_quality               # Mode kualitas ultra tinggi
--headless                    # Jalankan tanpa tampilan browser
```

### **Method Options**
| Method | Kecepatan | Kualitas | Deskripsi |
|--------|-----------|----------|-----------|
| `click_and_view` | Sedang | Tinggi | Klik gambar, lihat full size, capture |
| `direct_url` | Cepat | Standar | Ambil URL langsung tanpa klik |

## 🎯 Contoh Penggunaan Lengkap

### **1. Pemula - Coba Dulu (3 gambar)**
```bash
python scraperBot.py --mode ebird \
  --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" \
  --max_images 3
```

### **2. Standar - Kualitas Bagus (20 gambar)**
```bash
python scraperBot.py --mode ebird \
  --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" \
  --method click_and_view \
  --max_images 20 \
  --out_directory "./java_sparrow_photos"
```

### **3. Kualitas Tinggi - Untuk Koleksi**
```bash
python scraperBot.py --mode ebird \
  --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" \
  --method click_and_view \
  --ultra_quality \
  --max_images 30 \
  --timeout 90 \
  --out_directory "./high_quality_collection"
```

### **4. Bulk Download - Banyak Gambar**
```bash
python scraperBot.py --mode ebird \
  --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" \
  --method direct_url \
  --headless \
  --max_images 100 \
  --timeout 120 \
  --out_directory "./bulk_download"
```

### **5. Background Processing - Tanpa GUI**
```bash
python scraperBot.py --mode ebird \
  --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" \
  --headless \
  --max_images 50 \
  --timeout 60 \
  --out_directory "./background_photos"
```

### **6. Custom Folder dengan Tanggal**
```bash
python scraperBot.py --mode ebird \
  --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" \
  --max_images 25 \
  --out_directory "./photos/java_sparrow_2024_01_15"
```

## 🔧 Cara Mendapatkan URL eBird

### **Langkah-langkah:**
1. **Buka eBird.org**
2. **Pilih "Media Catalog"**
3. **Filter berdasarkan:**
   - Region: Indonesia (ID)
   - Species: Pilih burung yang diinginkan
   - Media Type: Photos
4. **Copy URL dari address bar**

### **Format URL eBird:**
```
https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=SPECIES_CODE
```

### **Contoh Species Code:**
| Burung | Species Code | URL |
|--------|--------------|-----|
| Java Sparrow | javmun1 | `taxonCode=javmun1` |
| White-rumped Shama | whirsh1 | `taxonCode=whirsh1` |
| Asian Koel | asikoe2 | `taxonCode=asikoe2` |
| Oriental Magpie-Robin | orimagr1 | `taxonCode=orimagr1` |

## 💡 Tips & Rekomendasi

### **Untuk Pemula:**
```bash
# Mulai dengan jumlah kecil
--max_images 5

# Jangan gunakan headless dulu (biar bisa lihat prosesnya)
# Jangan tambahkan --headless

# Gunakan timeout yang cukup
--timeout 30
```

### **Untuk Kualitas Terbaik:**
```bash
--method click_and_view
--ultra_quality
--timeout 90
```

### **Untuk Kecepatan:**
```bash
--method direct_url
--headless
--timeout 30
```

### **Untuk Stabilitas:**
```bash
--max_images 25    # Jangan terlalu banyak sekaligus
--timeout 60       # Timeout yang cukup
```

## 🚨 Troubleshooting

### **Problem: "No clickable images found"**
**Solusi:**
```bash
# Cek URL-nya benar
# Tambah timeout
--timeout 60

# Coba tanpa headless
# Hapus --headless
```

### **Problem: "Download failed"**
**Solusi:**
```bash
# Tambah timeout
--timeout 90

# Ganti method
--method direct_url

# Kurangi jumlah gambar
--max_images 10
```

### **Problem: Browser crash**
**Solusi:**
```bash
# Gunakan headless
--headless

# Kurangi jumlah gambar
--max_images 20

# Restart komputer jika perlu
```

### **Problem: Gambar kualitas rendah**
**Solusi:**
```bash
# Aktifkan ultra quality
--ultra_quality

# Gunakan click_and_view
--method click_and_view
```

## 📁 Organisasi File

### **Struktur Folder Recommended:**
```
eBird_Photos/
├── java_sparrow/
│   ├── 2024_01_15/
│   └── 2024_01_16/
├── white_rumped_shama/
│   ├── 2024_01_15/
│   └── 2024_01_16/
└── asian_koel/
    ├── 2024_01_15/
    └── 2024_01_16/
```

### **Command untuk Struktur di atas:**
```bash
# Java Sparrow
python scraperBot.py --mode ebird \
  --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" \
  --out_directory "./eBird_Photos/java_sparrow/2024_01_15" \
  --max_images 30

# White-rumped Shama
python scraperBot.py --mode ebird \
  --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=whirsh1" \
  --out_directory "./eBird_Photos/white_rumped_shama/2024_01_15" \
  --max_images 30
```

## 🎯 Rekomendasi Berdasarkan Kebutuhan

### **Untuk Hobi/Koleksi Pribadi:**
```bash
python scraperBot.py --mode ebird \
  --ebird_url "YOUR_URL" \
  --method click_and_view \
  --max_images 25 \
  --timeout 45 \
  --out_directory "./my_bird_collection"
```

### **Untuk Penelitian/Studi:**
```bash
python scraperBot.py --mode ebird \
  --ebird_url "YOUR_URL" \
  --method click_and_view \
  --ultra_quality \
  --max_images 50 \
  --timeout 90 \
  --headless \
  --out_directory "./research_data"
```

### **Untuk Monitoring Rutin:**
```bash
python scraperBot.py --mode ebird \
  --ebird_url "YOUR_URL" \
  --headless \
  --max_images 30 \
  --timeout 45 \
  --out_directory "./monitoring/$(date +%Y%m%d)"
```

## 🔄 Batch Processing

### **Windows Batch File (process_multiple.bat):**
```batch
@echo off
echo Starting eBird batch processing...

python scraperBot.py --mode ebird --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" --out_directory "./photos/java_sparrow" --max_images 30 --headless

python scraperBot.py --mode ebird --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=whirsh1" --out_directory "./photos/white_rumped_shama" --max_images 30 --headless

echo Batch processing complete!
pause
```

### **Linux/Mac Script (process_multiple.sh):**
```bash
#!/bin/bash
echo "Starting eBird batch processing..."

python scraperBot.py --mode ebird --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" --out_directory "./photos/java_sparrow" --max_images 30 --headless

python scraperBot.py --mode ebird --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=whirsh1" --out_directory "./photos/white_rumped_shama" --max_images 30 --headless

echo "Batch processing complete!"
```

Sekarang dokumentasi sudah lengkap dengan semua opsi penggunaan! 🎉
