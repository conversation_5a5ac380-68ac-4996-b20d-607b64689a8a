#!/usr/bin/env python3
"""
Enhanced Navigation Flow Test Script

This script tests the enhanced navigation flow that ensures proper page management
after each screenshot. It demonstrates the reliable, repeatable process:
click thumbnail → view full image → capture → return to gallery → repeat

Features tested:
1. Proper modal/lightbox closing
2. Navigation back to main gallery
3. Page readiness verification
4. Error recovery mechanisms
5. Flow control between images
"""

import os
import sys
import time
from scraperBot import EBirdScraperBot

def test_enhanced_navigation_flow():
    """Test the enhanced navigation flow with detailed monitoring"""
    print("🧪 TESTING ENHANCED NAVIGATION FLOW")
    print("=" * 60)
    
    # Test configuration
    test_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
    test_dir = os.path.join(os.getcwd(), "navigation_flow_test")
    
    print(f"📍 Test URL: {test_url}")
    print(f"📁 Output directory: {test_dir}")
    print(f"🎯 Testing navigation flow with 5 images")
    print("=" * 60)
    
    try:
        # Create output directory
        os.makedirs(test_dir, exist_ok=True)
        
        # Use GUI mode to visually monitor the navigation flow
        print("\n🖥️ Starting in GUI mode to monitor navigation flow...")
        scraper = EBirdScraperBot(headless=False)
        
        print("\n🔄 Testing enhanced navigation flow...")
        print("Watch the browser to see the navigation behavior:")
        print("  1. Click thumbnail")
        print("  2. View full image")
        print("  3. Capture image")
        print("  4. Return to gallery")
        print("  5. Repeat for next image")
        
        start_time = time.time()
        result_count = scraper.scrape_ebird(
            ebird_url=test_url,
            output_dir=test_dir,
            max_images=5,  # Test with 5 images to see the flow
            method='click_and_view',
            timeout_minutes=15
        )
        
        test_time = time.time() - start_time
        
        print(f"\n✅ Enhanced navigation test completed in {test_time:.1f} seconds")
        print(f"📊 Images processed: {result_count}")
        
        # Analyze the results
        analyze_navigation_results(test_dir)
        
        return True
        
    except Exception as e:
        print(f"❌ Navigation flow test failed: {e}")
        return False

def analyze_navigation_results(test_dir):
    """Analyze the results of the navigation flow test"""
    print("\n🔍 ANALYZING NAVIGATION FLOW RESULTS")
    print("-" * 40)
    
    if not os.path.exists(test_dir):
        print("❌ Test directory not found")
        return
    
    image_files = [f for f in os.listdir(test_dir) if f.endswith(('.jpg', '.png'))]
    
    if not image_files:
        print("❌ No images found - navigation flow may have failed")
        return
    
    print(f"📁 Found {len(image_files)} images:")
    
    for i, filename in enumerate(image_files, 1):
        filepath = os.path.join(test_dir, filename)
        file_size = os.path.getsize(filepath)
        file_size_kb = file_size / 1024
        
        print(f"  {i}. {filename} ({file_size_kb:.1f} KB)")
    
    # Navigation flow assessment
    if len(image_files) >= 3:
        print(f"\n✅ NAVIGATION FLOW SUCCESS!")
        print(f"   Successfully processed {len(image_files)} images in sequence")
        print(f"   This indicates the navigation flow is working correctly")
    elif len(image_files) >= 1:
        print(f"\n⚠️ PARTIAL SUCCESS")
        print(f"   Processed {len(image_files)} images")
        print(f"   Navigation flow may need optimization")
    else:
        print(f"\n❌ NAVIGATION FLOW FAILED")
        print(f"   No images processed successfully")

def test_navigation_components():
    """Test individual navigation components"""
    print("\n🧪 TESTING INDIVIDUAL NAVIGATION COMPONENTS")
    print("=" * 60)
    
    test_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
    
    try:
        scraper = EBirdScraperBot(headless=False)
        
        # Test 1: Page loading and gallery detection
        print("\n1. Testing page loading and gallery detection...")
        scraper.wd.get(test_url)
        time.sleep(3)
        
        if scraper._verify_main_page():
            print("   ✅ Main page verification successful")
        else:
            print("   ❌ Main page verification failed")
        
        if scraper._wait_for_gallery_ready():
            print("   ✅ Gallery readiness check successful")
        else:
            print("   ❌ Gallery readiness check failed")
        
        # Test 2: Clickable images detection
        print("\n2. Testing clickable images detection...")
        clickable_images = scraper._get_clickable_images_from_page()
        print(f"   Found {len(clickable_images)} clickable images")
        
        if len(clickable_images) > 0:
            print("   ✅ Clickable images detection successful")
        else:
            print("   ❌ No clickable images found")
        
        # Test 3: Navigation readiness verification
        print("\n3. Testing navigation readiness verification...")
        if scraper._verify_ready_for_next_image():
            print("   ✅ Ready for next image verification successful")
        else:
            print("   ❌ Not ready for next image")
        
        # Test 4: Page recovery (simulate)
        print("\n4. Testing page recovery mechanism...")
        if scraper._recover_page_state():
            print("   ✅ Page recovery successful")
        else:
            print("   ❌ Page recovery failed")
        
        scraper.wd.quit()
        
        print("\n📊 COMPONENT TEST SUMMARY:")
        print("   All individual navigation components tested")
        print("   Check results above for any issues")
        
        return True
        
    except Exception as e:
        print(f"❌ Component testing failed: {e}")
        try:
            scraper.wd.quit()
        except:
            pass
        return False

def demonstrate_navigation_features():
    """Demonstrate the key navigation features"""
    print("\n📋 ENHANCED NAVIGATION FEATURES")
    print("=" * 60)
    
    features = [
        {
            "name": "Modal/Lightbox Closing",
            "description": "Automatically closes image viewers after capture",
            "methods": ["ESC key", "Close buttons", "Overlay clicking"]
        },
        {
            "name": "Main Page Return",
            "description": "Ensures return to main gallery page",
            "methods": ["Window management", "URL verification", "Gallery detection"]
        },
        {
            "name": "Page Readiness Verification", 
            "description": "Confirms page is ready for next image",
            "methods": ["Clickable images check", "Gallery state verification"]
        },
        {
            "name": "Error Recovery",
            "description": "Recovers from navigation failures",
            "methods": ["Page refresh", "URL navigation", "Browser back button"]
        },
        {
            "name": "Flow Control",
            "description": "Manages the processing sequence",
            "methods": ["State verification", "Progress tracking", "Dynamic updates"]
        }
    ]
    
    for i, feature in enumerate(features, 1):
        print(f"\n{i}. {feature['name']}")
        print(f"   📝 {feature['description']}")
        print(f"   🔧 Methods: {', '.join(feature['methods'])}")
    
    print(f"\n🎯 NAVIGATION FLOW SEQUENCE:")
    print(f"   1. 🖱️ Click thumbnail image")
    print(f"   2. ⏳ Wait for full image to load")
    print(f"   3. 📸 Capture image (URL or screenshot)")
    print(f"   4. 🔄 Close modal/viewer")
    print(f"   5. 🏠 Return to main gallery")
    print(f"   6. ✅ Verify page readiness")
    print(f"   7. 🔁 Repeat for next image")

def main():
    print("🚀 ENHANCED NAVIGATION FLOW TEST SUITE")
    print("=" * 60)
    print("This script tests the enhanced navigation flow that ensures")
    print("reliable page management after each screenshot capture.")
    print()
    
    # Show navigation features
    demonstrate_navigation_features()
    
    print("\n" + "=" * 60)
    print("Choose a test to run:")
    print("1. Full navigation flow test (5 images)")
    print("2. Individual component testing")
    print("3. Show features only")
    
    choice = input("\nEnter choice (1-3): ").strip()
    
    if choice == "1":
        success = test_enhanced_navigation_flow()
    elif choice == "2":
        success = test_navigation_components()
    elif choice == "3":
        success = True  # Already shown above
    else:
        print("Invalid choice. Showing features only.")
        success = True
    
    if success:
        print("\n🎊 Enhanced navigation flow testing completed!")
        print("The scraper now provides reliable page management")
        print("that mimics manual browsing behavior.")
    else:
        print("\n⚠️ Some tests encountered issues.")
        print("Check the error messages above for troubleshooting.")

if __name__ == "__main__":
    main()
