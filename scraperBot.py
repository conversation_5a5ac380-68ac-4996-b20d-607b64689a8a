from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import os
import time
from urllib import request as wget
import tqdm as tqdm
import argparse
import requests
import json
from urllib.parse import urljoin, urlparse
'''

wd = webdriver.Chrome(executable_path=os.path.join(os.getcwd(), 'utils\\chromedriver.exe'))
wd.get('https://google.com')
search = wd.find_element_by_css_selector('input.gLFyf')
search.send_keys('')

'''

class ScraperBot():
    def __init__(self):
        self.wd = webdriver.Chrome(executable_path=os.path.join(os.getcwd(), 'chromedriver.exe'))
        self.urlbase = "https://google.com/search?tbm=isch&q={}"

    def _initiateSession(self, key):
        self.wd.get(self.urlbase.format(key))

    def _acceptCookies(self):
        try:
            self.wd.execute_script("document.getElementsByClassName('USRMqe')[0].style.display = 'none';")
        except:
            pass

    def _endOfPage(self):
        try:
            self.wd.find_element_by_class_name('OuJzKb Yu2Dnd')
            print("no more files")
        except:
            pass

        try:
            self.wd.find_element_by_class_name('mye4qd').click()
            time.sleep(1)
            self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        except:
            self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")


    def _deduplicate(self, listOfurls):
        try:
            inputList = list(set(listOfurls))
            return inputList
        except:
            pass

    def _checkOpenTabs(self):
        browserTabs = self.wd.window_handles
        if len(browserTabs) > 1:
            self.wd.switch_to.window(browserTabs[1])
            self.wd.close()
            self.wd.switch_to.window(browserTabs[0])

    def _getURL(self):
        thumbs = self.wd.find_elements_by_css_selector('img.Q4LuWd')
        urls = []
        for thumbImg in thumbs:
            try:
                thumbImg.click()
                actualImg = self.wd.find_elements_by_css_selector('img.n3VNCb')

                for imageData in actualImg:
                    if 'https' in imageData.get_attribute('src'):
                        urls.append(imageData.get_attribute('src'))

                self._checkOpenTabs()
            except:
                pass
        return urls

    def _totalImages(self, dir):
        count = 0
        for filename in os.listdir(dir):
            if filename.endswith('.jpg'):
                count += 1
            else:
                continue
        return count

    def _downloader(self, data, key, out_dir):
        key = key.replace(" ", "_")
        DIR1 = os.path.join(out_dir, key)

        try:
            os.mkdir(DIR1)
        except:
            pass

        for idx in tqdm.tqdm(range(len(data))):
            filename = "{}-{}.jpg".format(key, idx)
            PATH = os.path.join(DIR1, '{}'.format(filename))

            try:
                print("downloading next batch")
                wget.urlretrieve(str(data[idx]), PATH)
            except:
                pass


    def scrape(self, search, min_image_count, directory):
        self._initiateSession(key=search)
        self._acceptCookies()

        totalImageCount = 0
        while totalImageCount < min_image_count:
            urlList = self._deduplicate(self._getURL())
            self._downloader(data=urlList,
                             key=search,
                             out_dir=directory)
            urlList.clear()
            totalImageCount = self._totalImages(os.path.join(os.getcwd(), search.replace(" ", "_")))
            print("current Image count: {}".format(totalImageCount))
            self._endOfPage()
            time.sleep(2)

        if totalImageCount >= min_image_count:
            self.wd.quit()


class EBirdScraperBot():
    def __init__(self, headless=False):
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # Gunakan webdriver-manager untuk otomatis download ChromeDriver
        try:
            service = Service(ChromeDriverManager().install())
            self.wd = webdriver.Chrome(service=service, options=chrome_options)
        except Exception as e:
            print(f"Error initializing Chrome driver: {e}")
            # Fallback ke cara lama jika webdriver-manager gagal
            try:
                self.wd = webdriver.Chrome(executable_path=os.path.join(os.getcwd(), 'chromedriver.exe'), options=chrome_options)
            except:
                self.wd = webdriver.Chrome(options=chrome_options)

        # Disable webdriver detection
        self.wd.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        self.wait = WebDriverWait(self.wd, 10)
        self.base_url = "https://media.ebird.org"

    def _scroll_and_load_more(self, max_attempts=10, timeout_seconds=300):
        """Enhanced scroll and load more functionality with automatic 'Load More' button detection"""
        print("Starting enhanced scroll and load more process...")

        start_time = time.time()
        attempts = 0
        consecutive_no_change = 0
        max_consecutive_no_change = 3

        last_height = self.wd.execute_script("return document.body.scrollHeight")
        last_image_count = len(self._get_current_image_elements())

        while attempts < max_attempts and (time.time() - start_time) < timeout_seconds:
            attempts += 1
            print(f"Scroll attempt {attempts}/{max_attempts}")

            # Scroll to bottom
            self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)

            # Check for and click "Load More" button
            load_more_clicked = self._click_load_more_button()
            if load_more_clicked:
                print("✓ Load More button clicked, waiting for content to load...")
                time.sleep(5)  # Wait longer after clicking Load More

            # Check if page height changed
            new_height = self.wd.execute_script("return document.body.scrollHeight")
            current_image_count = len(self._get_current_image_elements())

            print(f"Height: {last_height} → {new_height}, Images: {last_image_count} → {current_image_count}")

            # If no change in height and image count, increment counter
            if new_height == last_height and current_image_count == last_image_count:
                consecutive_no_change += 1
                print(f"No change detected ({consecutive_no_change}/{max_consecutive_no_change})")

                if consecutive_no_change >= max_consecutive_no_change:
                    print("No more content loading, stopping scroll process")
                    break
            else:
                consecutive_no_change = 0  # Reset counter if there was a change

            last_height = new_height
            last_image_count = current_image_count

            # Additional wait for content to stabilize
            time.sleep(1)

        total_time = time.time() - start_time
        final_image_count = len(self._get_current_image_elements())
        print(f"Scroll process completed in {total_time:.1f}s. Final image count: {final_image_count}")

    def _get_current_image_elements(self):
        """Get current count of image elements on the page"""
        try:
            selectors = [
                'img[src*="macaulaylibrary.org"]',
                'img[src*="cdn.download.ams.birds.cornell.edu"]',
                'img[data-src*="macaulaylibrary.org"]',
                'img[data-src*="cdn.download.ams.birds.cornell.edu"]',
                '.MediaCard img',
                '.media-card img',
                '.photo-card img'
            ]

            all_images = []
            for selector in selectors:
                try:
                    images = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    all_images.extend(images)
                except:
                    continue

            # Remove duplicates based on src attribute
            unique_images = []
            seen_srcs = set()
            for img in all_images:
                src = img.get_attribute('src') or img.get_attribute('data-src')
                if src and src not in seen_srcs:
                    unique_images.append(img)
                    seen_srcs.add(src)

            return unique_images
        except Exception as e:
            print(f"Error getting current image elements: {e}")
            return []

    def _click_load_more_button(self):
        """Detect and click 'Load More' button if available"""
        load_more_selectors = [
            'button[data-testid="load-more"]',
            'button:contains("Load More")',
            'button:contains("Show More")',
            'button:contains("More")',
            '.load-more-button',
            '.show-more-button',
            'button[class*="load"]',
            'button[class*="more"]',
            'a[href*="offset"]',  # Pagination links
            '.pagination a:last-child',
            'button[aria-label*="more"]',
            'button[aria-label*="load"]'
        ]

        for selector in load_more_selectors:
            try:
                # Handle :contains() pseudo-selector manually
                if ':contains(' in selector:
                    text_to_find = selector.split(':contains("')[1].split('")')[0]
                    buttons = self.wd.find_elements(By.TAG_NAME, 'button')
                    for button in buttons:
                        if text_to_find.lower() in button.text.lower():
                            if button.is_displayed() and button.is_enabled():
                                print(f"Found and clicking Load More button: '{button.text}'")
                                self.wd.execute_script("arguments[0].scrollIntoView(true);", button)
                                time.sleep(1)
                                button.click()
                                return True
                else:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            print(f"Found and clicking Load More element with selector: {selector}")
                            self.wd.execute_script("arguments[0].scrollIntoView(true);", element)
                            time.sleep(1)
                            element.click()
                            return True
            except Exception as e:
                continue

        return False

    def _get_clickable_images_from_page(self):
        """Cari semua gambar yang bisa diklik untuk melihat versi penuh"""
        clickable_images = []

        try:
            # Tunggu sampai halaman dimuat
            time.sleep(3)

            # Cari elemen gambar yang bisa diklik (biasanya dalam link atau card)
            selectors = [
                "a[href*='/catalog/'] img",  # Gambar dalam link catalog
                ".MediaCard img",            # Gambar dalam MediaCard
                ".MediaThumbnail img",       # Gambar thumbnail
                "[data-testid='media-card'] img",  # Gambar dalam media card
                "img[src*='macaulaylibrary.org']",  # Gambar langsung
                "img[src*='cdn.download.ams.birds.cornell.edu']"  # Gambar CDN
            ]

            for selector in selectors:
                try:
                    images = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    print(f"Found {len(images)} clickable images with selector: {selector}")

                    for img in images:
                        # Cek apakah gambar bisa diklik (ada parent link atau clickable)
                        parent_link = None
                        try:
                            # Cari parent link
                            parent_link = img.find_element(By.XPATH, "./ancestor::a[1]")
                        except:
                            # Jika tidak ada parent link, coba klik gambar langsung
                            pass

                        clickable_images.append({
                            'element': img,
                            'parent_link': parent_link,
                            'src': img.get_attribute('src') or img.get_attribute('data-src')
                        })

                except Exception as e:
                    print(f"Error with selector {selector}: {e}")
                    continue

            print(f"Total clickable images found: {len(clickable_images)}")
            return clickable_images

        except Exception as e:
            print(f"Error finding clickable images: {e}")
            return []

    def _click_and_get_full_image(self, clickable_img, index, output_dir=None):
        """Enhanced click and get full image with better error handling"""
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                print(f"🖱️ Clicking image {index + 1} (attempt {retry_count + 1}/{max_retries})")

                # Scroll element into view with better positioning
                self.wd.execute_script("""
                    arguments[0].scrollIntoView({
                        behavior: 'smooth',
                        block: 'center',
                        inline: 'center'
                    });
                """, clickable_img['element'])
                time.sleep(2)

                # Wait for element to be clickable
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC

                try:
                    if clickable_img['parent_link']:
                        print(f"🔗 Clicking parent link for image {index + 1}")
                        WebDriverWait(self.wd, 10).until(
                            EC.element_to_be_clickable(clickable_img['parent_link'])
                        )
                        clickable_img['parent_link'].click()
                    else:
                        print(f"🖼️ Clicking image {index + 1} directly")
                        WebDriverWait(self.wd, 10).until(
                            EC.element_to_be_clickable(clickable_img['element'])
                        )
                        clickable_img['element'].click()
                except Exception as click_error:
                    print(f"⚠️ Click failed, trying JavaScript click: {click_error}")
                    # Fallback to JavaScript click
                    element_to_click = clickable_img['parent_link'] or clickable_img['element']
                    self.wd.execute_script("arguments[0].click();", element_to_click)

                # Wait for content to load
                time.sleep(4)

                # Try to find full resolution image first
                full_image_url = self._find_full_resolution_image()

                if full_image_url:
                    print(f"✅ Found full resolution URL: {full_image_url[:80]}...")
                    return full_image_url
                else:
                    # Take enhanced screenshot as fallback
                    print(f"📸 No URL found, taking enhanced screenshot for image {index + 1}")
                    screenshot_path = self._take_screenshot(index, output_dir)
                    if screenshot_path:
                        return screenshot_path
                    else:
                        print(f"❌ Screenshot also failed for image {index + 1}")
                        return None

            except Exception as e:
                retry_count += 1
                error_msg = f"Error clicking image {index + 1} (attempt {retry_count}): {e}"
                print(f"⚠️ {error_msg}")

                if retry_count < max_retries:
                    print(f"🔄 Retrying in 2 seconds...")
                    time.sleep(2)
                    # Try to return to main page before retry
                    self._return_to_main_page()
                else:
                    print(f"❌ All attempts failed for image {index + 1}")
                    return None
            finally:
                # Always try to return to main page
                self._return_to_main_page()

        return None

    def _find_full_resolution_image(self):
        """Cari gambar resolusi penuh setelah klik"""
        try:
            # Berbagai selector untuk gambar resolusi penuh
            full_image_selectors = [
                "img[src*='macaulaylibrary.org']:not([src*='thumbnail'])",
                "img[src*='cdn.download.ams.birds.cornell.edu']:not([src*='thumbnail'])",
                ".MediaDisplay img",
                ".MediaViewer img",
                ".FullscreenImage img",
                "[data-testid='media-display'] img",
                ".modal img",
                ".lightbox img",
                "img[alt*='photo']",
                "img[style*='max-width']"  # Gambar dengan style max-width biasanya full size
            ]

            for selector in full_image_selectors:
                try:
                    full_img = self.wd.find_element(By.CSS_SELECTOR, selector)
                    img_url = full_img.get_attribute('src')

                    if img_url and any(domain in img_url for domain in ['macaulaylibrary.org', 'cdn.download.ams.birds.cornell.edu']):
                        # Pastikan ini bukan thumbnail
                        if 'thumbnail' not in img_url.lower() and 'thumb' not in img_url.lower():
                            return self._convert_to_high_res(img_url)

                except:
                    continue

            return None

        except Exception as e:
            print(f"Error finding full resolution image: {e}")
            return None

    def _take_screenshot(self, index, output_dir=None):
        """Enhanced screenshot functionality with overlay handling and high-quality capture"""
        try:
            print(f"Taking high-quality screenshot for image {index + 1}...")

            # Set output directory
            if output_dir is None:
                output_dir = os.getcwd()

            # Wait for any animations or loading to complete
            time.sleep(2)

            # Hide overlays and UI elements that might interfere
            self._hide_ui_overlays()

            # Find the best image element to screenshot
            screenshot_element = self._find_best_screenshot_target()

            if screenshot_element:
                # Scroll element into view and center it
                self.wd.execute_script("""
                    arguments[0].scrollIntoView({
                        behavior: 'smooth',
                        block: 'center',
                        inline: 'center'
                    });
                """, screenshot_element)
                time.sleep(1)

                # Take element-specific screenshot
                screenshot_filename = f"ebird_screenshot_{index + 1:03d}.png"
                screenshot_path = os.path.join(output_dir, screenshot_filename)

                # Use element screenshot for better quality
                screenshot_element.screenshot(screenshot_path)

                # Verify screenshot was created and has reasonable size
                if os.path.exists(screenshot_path) and os.path.getsize(screenshot_path) > 1000:
                    print(f"✓ High-quality screenshot saved: {screenshot_filename}")
                    return screenshot_path
                else:
                    print("Element screenshot failed, trying full page screenshot...")
                    return self._take_fallback_screenshot(index, output_dir)

            else:
                print("No suitable image element found, taking full page screenshot...")
                return self._take_fallback_screenshot(index, output_dir)

        except Exception as e:
            print(f"Error taking screenshot: {e}")
            return self._take_fallback_screenshot(index, output_dir)

    def _hide_ui_overlays(self):
        """Hide UI overlays and elements that might interfere with screenshots"""
        try:
            # Common overlay selectors to hide
            overlay_selectors = [
                '.overlay',
                '.modal-overlay',
                '.popup-overlay',
                '.tooltip',
                '.dropdown-menu',
                'nav',
                'header',
                '.navigation',
                '.toolbar',
                '.controls',
                '.ui-controls',
                '[class*="overlay"]',
                '[class*="popup"]',
                '[class*="tooltip"]'
            ]

            hidden_elements = []
            for selector in overlay_selectors:
                try:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            # Store original display style
                            original_style = element.get_attribute('style')
                            # Hide element
                            self.wd.execute_script("arguments[0].style.display = 'none';", element)
                            hidden_elements.append((element, original_style))
                except:
                    continue

            # Store hidden elements for potential restoration
            self._hidden_elements = hidden_elements

        except Exception as e:
            print(f"Warning: Could not hide UI overlays: {e}")

    def _find_best_screenshot_target(self):
        """Find the best image element for screenshot"""
        try:
            # Priority-ordered selectors for finding the main image
            image_selectors = [
                # Modal/viewer images (highest priority)
                '.modal img[src*="macaulaylibrary.org"]',
                '.viewer img[src*="macaulaylibrary.org"]',
                '.lightbox img[src*="macaulaylibrary.org"]',
                '.fullscreen img[src*="macaulaylibrary.org"]',
                '.MediaDisplay img',
                '.MediaViewer img',
                '.FullscreenImage img',
                '[data-testid="media-display"] img',

                # Large display images
                'img[class*="large"][src*="macaulaylibrary.org"]',
                'img[class*="full"][src*="macaulaylibrary.org"]',
                'img[class*="detail"][src*="macaulaylibrary.org"]',

                # General eBird images
                'img[src*="macaulaylibrary.org"]',
                'img[src*="cdn.download.ams.birds.cornell.edu"]',

                # Fallback to any large visible image
                'img[width][height]'
            ]

            best_element = None
            best_size = 0

            for selector in image_selectors:
                try:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            # Calculate element size
                            size = element.size
                            element_area = size['width'] * size['height']

                            # Prefer larger images
                            if element_area > best_size:
                                best_element = element
                                best_size = element_area

                except Exception as e:
                    continue

            if best_element:
                print(f"Found best screenshot target: {best_size} pixels area")
                return best_element
            else:
                print("No suitable screenshot target found")
                return None

        except Exception as e:
            print(f"Error finding screenshot target: {e}")
            return None

    def _take_fallback_screenshot(self, index, output_dir):
        """Take a fallback full-page screenshot"""
        try:
            screenshot_filename = f"ebird_fullpage_{index + 1:03d}.png"
            screenshot_path = os.path.join(output_dir, screenshot_filename)

            self.wd.save_screenshot(screenshot_path)

            if os.path.exists(screenshot_path) and os.path.getsize(screenshot_path) > 1000:
                print(f"✓ Fallback screenshot saved: {screenshot_filename}")
                return screenshot_path
            else:
                print(f"✗ Failed to create fallback screenshot")
                return None

        except Exception as e:
            print(f"Error taking fallback screenshot: {e}")
            return None

    def _return_to_main_page(self):
        """Kembali ke halaman utama"""
        try:
            # Jika ada multiple windows/tabs, tutup yang baru dan kembali ke utama
            if len(self.wd.window_handles) > 1:
                self.wd.close()
                self.wd.switch_to.window(self.wd.window_handles[0])
            else:
                # Jika modal/overlay, coba tutup dengan ESC atau tombol close
                try:
                    # Coba tekan ESC
                    from selenium.webdriver.common.keys import Keys
                    self.wd.find_element(By.TAG_NAME, 'body').send_keys(Keys.ESCAPE)
                    time.sleep(1)
                except:
                    pass

                # Coba cari tombol close
                close_selectors = [
                    ".close", ".modal-close", ".lightbox-close",
                    "[aria-label='Close']", "[title='Close']",
                    ".fa-times", ".fa-close", ".icon-close"
                ]

                for selector in close_selectors:
                    try:
                        close_btn = self.wd.find_element(By.CSS_SELECTOR, selector)
                        close_btn.click()
                        time.sleep(1)
                        break
                    except:
                        continue

        except Exception as e:
            print(f"Error returning to main page: {e}")

    def _get_image_urls_from_page(self):
        """Ekstrak URL gambar dengan metode klik dan lihat (DEPRECATED - gunakan _click_and_download_images)"""
        # Method lama untuk backward compatibility
        return self._click_and_download_images(max_images=50, download_method='url_only')

    def _convert_to_high_res(self, img_url):
        """Konversi URL gambar ke resolusi tinggi"""
        if not img_url:
            return None

        try:
            # Berbagai pola untuk konversi ke resolusi tinggi
            if 'macaulaylibrary.org' in img_url:
                # Ganti parameter size jika ada
                if '?size=' in img_url:
                    img_url = img_url.split('?size=')[0]

                # Tambahkan parameter untuk resolusi tinggi
                if '?' in img_url:
                    img_url += '&size=large'
                else:
                    img_url += '?size=large'

            elif 'cdn.download.ams.birds.cornell.edu' in img_url:
                # Untuk CDN Cornell, coba ganti ukuran
                img_url = img_url.replace('/thumbnails/', '/catalog/')
                img_url = img_url.replace('_thumbnail', '')
                img_url = img_url.replace('_small', '')
                img_url = img_url.replace('_medium', '')

            return img_url

        except Exception as e:
            print(f"Error converting URL to high res: {e}")
            return img_url

    def _get_high_res_image_from_detail(self, detail_url):
        """Ambil URL gambar resolusi tinggi dari halaman detail"""
        try:
            # Buka tab baru untuk halaman detail
            self.wd.execute_script("window.open('');")
            self.wd.switch_to.window(self.wd.window_handles[1])

            self.wd.get(detail_url)
            time.sleep(3)

            # Cari gambar resolusi tinggi dengan berbagai selector
            selectors = [
                "img[src*='macaulaylibrary.org']:not([src*='thumbnail'])",
                "img[src*='cdn.download.ams.birds.cornell.edu']:not([src*='thumbnail'])",
                ".MediaDisplay img",
                ".MediaViewer img",
                "[data-testid='media-display'] img",
                "img[alt*='photo']",
                "img[alt*='image']"
            ]

            img_url = None
            for selector in selectors:
                try:
                    high_res_img = self.wd.find_element(By.CSS_SELECTOR, selector)
                    img_url = high_res_img.get_attribute('src')
                    if img_url and any(domain in img_url for domain in ['macaulaylibrary.org', 'cdn.download.ams.birds.cornell.edu']):
                        break
                except:
                    continue

            # Tutup tab dan kembali ke tab utama
            self.wd.close()
            self.wd.switch_to.window(self.wd.window_handles[0])

            return self._convert_to_high_res(img_url) if img_url else None

        except Exception as e:
            print(f"Error getting high res image from {detail_url}: {e}")
            # Jika ada error, tutup tab dan kembali ke tab utama
            try:
                if len(self.wd.window_handles) > 1:
                    self.wd.close()
                    self.wd.switch_to.window(self.wd.window_handles[0])
            except:
                pass
            return None

    def _download_image(self, img_url, filename, output_dir):
        """Download gambar dari URL"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(img_url, headers=headers, stream=True)
            response.raise_for_status()

            filepath = os.path.join(output_dir, filename)

            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            return True

        except Exception as e:
            print(f"Error downloading {img_url}: {e}")
            return False

    def _click_and_download_images(self, max_images=50, download_method='both'):
        """Method utama: klik gambar, lihat versi penuh, kemudian download/screenshot"""
        downloaded_items = []

        try:
            # Cari semua gambar yang bisa diklik
            clickable_images = self._get_clickable_images_from_page()

            if not clickable_images:
                print("No clickable images found")
                return []

            # Batasi jumlah gambar
            if max_images and len(clickable_images) > max_images:
                clickable_images = clickable_images[:max_images]

            print(f"Processing {len(clickable_images)} images...")

            for i, clickable_img in enumerate(tqdm.tqdm(clickable_images, desc="Processing images")):
                try:
                    # Klik dan dapatkan gambar penuh
                    result = self._click_and_get_full_image(clickable_img, i)

                    if result:
                        downloaded_items.append(result)

                    # Jeda untuk menghindari rate limiting
                    time.sleep(1)

                except Exception as e:
                    print(f"Error processing image {i+1}: {e}")
                    continue

            return downloaded_items

        except Exception as e:
            print(f"Error in click and download process: {e}")
            return []

    def scrape_ebird(self, ebird_url, output_dir, max_images=50, method='click_and_view', timeout_minutes=30):
        """Enhanced eBird scraper with robust error handling and bulk processing"""
        print("=" * 60)
        print("🦅 ENHANCED EBIRD SCRAPER STARTING")
        print("=" * 60)
        print(f"📍 URL: {ebird_url}")
        print(f"🎯 Method: {method}")
        print(f"📁 Output: {output_dir}")
        print(f"🔢 Max images: {max_images}")
        print(f"⏱️ Timeout: {timeout_minutes} minutes")
        print("=" * 60)

        start_time = time.time()
        timeout_seconds = timeout_minutes * 60

        # Statistics tracking
        stats = {
            'attempted': 0,
            'successful_downloads': 0,
            'successful_screenshots': 0,
            'failed': 0,
            'errors': []
        }

        try:
            # Create output directory
            os.makedirs(output_dir, exist_ok=True)
            print(f"✓ Output directory created: {output_dir}")

            # Open eBird page with error handling
            print("\n🌐 Loading eBird page...")
            try:
                self.wd.get(ebird_url)
                time.sleep(3)
                print("✓ Page loaded successfully")
            except Exception as e:
                print(f"✗ Failed to load page: {e}")
                return self._return_error_stats(stats, f"Failed to load page: {e}")

            # Enhanced scroll and load more with progress feedback
            print("\n📜 Loading all available images...")
            try:
                self._scroll_and_load_more(timeout_seconds=min(300, timeout_seconds//2))
                print("✓ Page scrolling and loading completed")
            except Exception as e:
                print(f"⚠️ Warning during page loading: {e}")
                stats['errors'].append(f"Page loading warning: {e}")

            downloaded_count = 0

            if method == 'click_and_view':
                downloaded_count = self._process_click_and_view_method(
                    output_dir, max_images, stats, timeout_seconds, start_time
                )

            # Fallback to direct URL method if needed
            if (method == 'direct_url' or downloaded_count == 0) and (time.time() - start_time) < timeout_seconds:
                print("\n🔄 Using direct URL method as fallback...")
                fallback_count = self._process_direct_url_method(
                    output_dir, max_images, stats, timeout_seconds, start_time
                )
                downloaded_count += fallback_count

        except Exception as e:
            print(f"\n💥 Critical error in scraping process: {e}")
            stats['errors'].append(f"Critical error: {e}")

        finally:
            # Always quit browser
            try:
                self.wd.quit()
            except:
                pass

        # Print final statistics
        self._print_final_stats(stats, start_time, output_dir)
        return stats['successful_downloads'] + stats['successful_screenshots']

    def _process_click_and_view_method(self, output_dir, max_images, stats, timeout_seconds, start_time):
        """Process images using click and view method with enhanced error handling"""
        print("\n🖱️ Using CLICK AND VIEW method...")

        try:
            # Find clickable images
            clickable_images = self._get_clickable_images_from_page()

            if not clickable_images:
                print("⚠️ No clickable images found")
                return 0

            # Limit images if specified
            if max_images and len(clickable_images) > max_images:
                clickable_images = clickable_images[:max_images]
                print(f"📊 Limited to {max_images} images (found {len(clickable_images)} total)")

            print(f"🎯 Processing {len(clickable_images)} images...")

            successful_count = 0

            for i, clickable_img in enumerate(tqdm.tqdm(clickable_images, desc="🖼️ Processing images")):
                # Check timeout
                if (time.time() - start_time) > timeout_seconds:
                    print(f"\n⏰ Timeout reached, stopping at image {i+1}")
                    break

                stats['attempted'] += 1

                try:
                    print(f"\n📸 Processing image {i+1}/{len(clickable_images)}")

                    # Click and get full image
                    result = self._click_and_get_full_image(clickable_img, i, output_dir)

                    if result:
                        if result.endswith('.png'):
                            # Screenshot result
                            import shutil
                            new_path = os.path.join(output_dir, os.path.basename(result))
                            if os.path.exists(result):
                                shutil.move(result, new_path)
                                stats['successful_screenshots'] += 1
                                successful_count += 1
                                print(f"✓ Screenshot saved: {os.path.basename(result)}")
                            else:
                                print(f"⚠️ Screenshot file not found: {result}")
                                stats['failed'] += 1
                        else:
                            # URL result - download image
                            filename = f"ebird_image_{i+1:03d}.jpg"
                            if self._download_image(result, filename, output_dir):
                                stats['successful_downloads'] += 1
                                successful_count += 1
                                print(f"✓ Image downloaded: {filename}")
                            else:
                                stats['failed'] += 1
                                print(f"✗ Failed to download: {filename}")
                    else:
                        stats['failed'] += 1
                        print(f"✗ No result for image {i+1}")

                    # Progress feedback
                    if (i + 1) % 5 == 0:
                        elapsed = time.time() - start_time
                        avg_time = elapsed / (i + 1)
                        remaining = (len(clickable_images) - i - 1) * avg_time
                        print(f"📊 Progress: {i+1}/{len(clickable_images)} | Success: {successful_count} | ETA: {remaining/60:.1f}min")

                    # Rate limiting
                    time.sleep(1)

                except Exception as e:
                    stats['failed'] += 1
                    error_msg = f"Error processing image {i+1}: {e}"
                    print(f"✗ {error_msg}")
                    stats['errors'].append(error_msg)
                    continue

            return successful_count

        except Exception as e:
            error_msg = f"Error in click and view method: {e}"
            print(f"💥 {error_msg}")
            stats['errors'].append(error_msg)
            return 0

    def _process_direct_url_method(self, output_dir, max_images, stats, timeout_seconds, start_time):
        """Process images using direct URL method"""
        try:
            image_urls = self._get_image_urls_old_method()
            print(f"🔗 Found {len(image_urls)} image URLs")

            if not image_urls:
                print("⚠️ No image URLs found")
                return 0

            # Limit images if specified
            if max_images and len(image_urls) > max_images:
                image_urls = image_urls[:max_images]

            successful_count = 0

            for i, img_url in enumerate(tqdm.tqdm(image_urls, desc="⬇️ Downloading images")):
                # Check timeout
                if (time.time() - start_time) > timeout_seconds:
                    print(f"\n⏰ Timeout reached, stopping at image {i+1}")
                    break

                stats['attempted'] += 1
                filename = f"ebird_image_{i+1:03d}.jpg"

                try:
                    if self._download_image(img_url, filename, output_dir):
                        stats['successful_downloads'] += 1
                        successful_count += 1
                        print(f"✓ Downloaded: {filename}")
                    else:
                        stats['failed'] += 1
                        print(f"✗ Failed: {filename}")
                except Exception as e:
                    stats['failed'] += 1
                    error_msg = f"Error downloading {filename}: {e}"
                    print(f"✗ {error_msg}")
                    stats['errors'].append(error_msg)

                time.sleep(0.5)  # Rate limiting

            return successful_count

        except Exception as e:
            error_msg = f"Error in direct URL method: {e}"
            print(f"💥 {error_msg}")
            stats['errors'].append(error_msg)
            return 0

    def _return_error_stats(self, stats, error_message):
        """Return error statistics"""
        stats['errors'].append(error_message)
        self._print_final_stats(stats, time.time(), "N/A")
        return 0

    def _print_final_stats(self, stats, start_time, output_dir):
        """Print comprehensive final statistics"""
        elapsed_time = time.time() - start_time
        total_successful = stats['successful_downloads'] + stats['successful_screenshots']

        print("\n" + "=" * 60)
        print("📊 SCRAPING COMPLETED - FINAL STATISTICS")
        print("=" * 60)
        print(f"⏱️ Total time: {elapsed_time/60:.1f} minutes")
        print(f"🎯 Images attempted: {stats['attempted']}")
        print(f"✅ Total successful: {total_successful}")
        print(f"  📥 Downloads: {stats['successful_downloads']}")
        print(f"  📸 Screenshots: {stats['successful_screenshots']}")
        print(f"❌ Failed: {stats['failed']}")

        if stats['attempted'] > 0:
            success_rate = (total_successful / stats['attempted']) * 100
            print(f"📈 Success rate: {success_rate:.1f}%")

        if total_successful > 0:
            avg_time = elapsed_time / total_successful
            print(f"⚡ Average time per image: {avg_time:.1f} seconds")

        print(f"📁 Output directory: {output_dir}")

        if stats['errors']:
            print(f"\n⚠️ Errors encountered ({len(stats['errors'])}):")
            for i, error in enumerate(stats['errors'][:5], 1):  # Show first 5 errors
                print(f"  {i}. {error}")
            if len(stats['errors']) > 5:
                print(f"  ... and {len(stats['errors']) - 5} more errors")

        print("=" * 60)

    def _get_image_urls_old_method(self):
        """Method lama untuk mengambil URL gambar langsung"""
        image_urls = []

        try:
            # Cari semua elemen gambar dengan berbagai selector
            selectors = [
                "img[src*='macaulaylibrary.org']",
                "img[src*='cdn.download.ams.birds.cornell.edu']",
                "img[data-src*='macaulaylibrary.org']",
                "img[data-src*='cdn.download.ams.birds.cornell.edu']",
                ".MediaCard img",
                ".MediaThumbnail img",
                "[data-testid='media-card'] img"
            ]

            for selector in selectors:
                try:
                    images = self.wd.find_elements(By.CSS_SELECTOR, selector)

                    for img in images:
                        # Coba ambil dari src atau data-src
                        img_url = img.get_attribute('src') or img.get_attribute('data-src')

                        if img_url and any(domain in img_url for domain in ['macaulaylibrary.org', 'cdn.download.ams.birds.cornell.edu']):
                            # Konversi ke URL resolusi tinggi
                            high_res_url = self._convert_to_high_res(img_url)
                            if high_res_url:
                                image_urls.append(high_res_url)

                except Exception as e:
                    continue

            # Hapus duplikat
            unique_urls = list(set(image_urls))
            return unique_urls

        except Exception as e:
            print(f"Error getting image URLs: {e}")
            return []


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Web scraper for Google Images and eBird photos")

    # Tambahkan pilihan mode
    parser.add_argument('--mode', choices=['google', 'ebird'], default='google',
                        help='Pilih mode scraping: google untuk Google Images, ebird untuk eBird')

    # Argumen untuk Google Images (mode lama)
    parser.add_argument('--search', type=str,
                        help='The keyword to search in Google Images')

    parser.add_argument('--min_image_count', type=int, default=1,
                        help='Minimum number of images for Google Images scraping')

    # Argumen untuk eBird
    parser.add_argument('--ebird_url', type=str,
                        help='URL eBird untuk scraping foto (contoh: https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1)')

    parser.add_argument('--max_images', type=int, default=50,
                        help='Maksimum jumlah gambar untuk didownload dari eBird')

    parser.add_argument('--method', choices=['click_and_view', 'direct_url'], default='click_and_view',
                        help='Metode scraping: click_and_view (klik gambar dulu, lihat versi penuh) atau direct_url (ambil URL langsung)')

    parser.add_argument('--timeout', type=int, default=30,
                        help='Timeout dalam menit untuk proses scraping (default: 30 menit)')

    # Argumen umum
    parser.add_argument('--out_directory', default=os.getcwd(),
                        help='The full path to the output directory')

    parser.add_argument('--headless', action='store_true',
                        help='Jalankan browser dalam mode headless (tanpa GUI)')

    args = parser.parse_args()

    if args.mode == 'google':
        if not args.search:
            print("Error: --search diperlukan untuk mode Google Images")
            exit(1)

        SEARCH = args.search
        IMAGECOUNT = args.min_image_count
        OUTDIR = args.out_directory

        scraperBot = ScraperBot()
        scraperBot.scrape(search=SEARCH, min_image_count=IMAGECOUNT, directory=OUTDIR)

    elif args.mode == 'ebird':
        if not args.ebird_url:
            print("Error: --ebird_url diperlukan untuk mode eBird")
            print("Contoh: python scraperBot.py --mode ebird --ebird_url 'https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1'")
            exit(1)

        ebird_scraper = EBirdScraperBot(headless=args.headless)
        ebird_scraper.scrape_ebird(
            ebird_url=args.ebird_url,
            output_dir=args.out_directory,
            max_images=args.max_images,
            method=args.method,
            timeout_minutes=args.timeout
        )