from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import os
import time
from urllib import request as wget
import tqdm as tqdm
import argparse
import requests
import json
from urllib.parse import urljoin, urlparse
'''

wd = webdriver.Chrome(executable_path=os.path.join(os.getcwd(), 'utils\\chromedriver.exe'))
wd.get('https://google.com')
search = wd.find_element_by_css_selector('input.gLFyf')
search.send_keys('')

'''

class ScraperBot():
    def __init__(self):
        self.wd = webdriver.Chrome(executable_path=os.path.join(os.getcwd(), 'chromedriver.exe'))
        self.urlbase = "https://google.com/search?tbm=isch&q={}"

    def _initiateSession(self, key):
        self.wd.get(self.urlbase.format(key))

    def _acceptCookies(self):
        try:
            self.wd.execute_script("document.getElementsByClassName('USRMqe')[0].style.display = 'none';")
        except:
            pass

    def _endOfPage(self):
        try:
            self.wd.find_element_by_class_name('OuJzKb Yu2Dnd')
            print("no more files")
        except:
            pass

        try:
            self.wd.find_element_by_class_name('mye4qd').click()
            time.sleep(1)
            self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        except:
            self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")


    def _deduplicate(self, listOfurls):
        try:
            inputList = list(set(listOfurls))
            return inputList
        except:
            pass

    def _checkOpenTabs(self):
        browserTabs = self.wd.window_handles
        if len(browserTabs) > 1:
            self.wd.switch_to.window(browserTabs[1])
            self.wd.close()
            self.wd.switch_to.window(browserTabs[0])

    def _getURL(self):
        thumbs = self.wd.find_elements_by_css_selector('img.Q4LuWd')
        urls = []
        for thumbImg in thumbs:
            try:
                thumbImg.click()
                actualImg = self.wd.find_elements_by_css_selector('img.n3VNCb')

                for imageData in actualImg:
                    if 'https' in imageData.get_attribute('src'):
                        urls.append(imageData.get_attribute('src'))

                self._checkOpenTabs()
            except:
                pass
        return urls

    def _totalImages(self, dir):
        count = 0
        for filename in os.listdir(dir):
            if filename.endswith('.jpg'):
                count += 1
            else:
                continue
        return count

    def _downloader(self, data, key, out_dir):
        key = key.replace(" ", "_")
        DIR1 = os.path.join(out_dir, key)

        try:
            os.mkdir(DIR1)
        except:
            pass

        for idx in tqdm.tqdm(range(len(data))):
            filename = "{}-{}.jpg".format(key, idx)
            PATH = os.path.join(DIR1, '{}'.format(filename))

            try:
                print("downloading next batch")
                wget.urlretrieve(str(data[idx]), PATH)
            except:
                pass


    def scrape(self, search, min_image_count, directory):
        self._initiateSession(key=search)
        self._acceptCookies()

        totalImageCount = 0
        while totalImageCount < min_image_count:
            urlList = self._deduplicate(self._getURL())
            self._downloader(data=urlList,
                             key=search,
                             out_dir=directory)
            urlList.clear()
            totalImageCount = self._totalImages(os.path.join(os.getcwd(), search.replace(" ", "_")))
            print("current Image count: {}".format(totalImageCount))
            self._endOfPage()
            time.sleep(2)

        if totalImageCount >= min_image_count:
            self.wd.quit()


class EBirdScraperBot():
    def __init__(self, headless=False, custom_resolution=None):
        # Initialize ultra quality mode
        self.ultra_quality_mode = False
        self.custom_resolution = custom_resolution or (1920, 1080)

        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")

        # Enhanced window size for better screenshots
        width, height = self.custom_resolution
        chrome_options.add_argument(f"--window-size={width},{height}")
        chrome_options.add_argument("--start-maximized")

        # High quality rendering options
        chrome_options.add_argument("--force-device-scale-factor=1")
        chrome_options.add_argument("--high-dpi-support=1")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")

        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # Gunakan webdriver-manager untuk otomatis download ChromeDriver
        try:
            service = Service(ChromeDriverManager().install())
            self.wd = webdriver.Chrome(service=service, options=chrome_options)
        except Exception as e:
            print(f"Error initializing Chrome driver: {e}")
            # Fallback ke cara lama jika webdriver-manager gagal
            try:
                self.wd = webdriver.Chrome(executable_path=os.path.join(os.getcwd(), 'chromedriver.exe'), options=chrome_options)
            except:
                self.wd = webdriver.Chrome(options=chrome_options)

        # Disable webdriver detection
        self.wd.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        # Set maximum window size for better screenshots
        self.wd.maximize_window()

        self.wait = WebDriverWait(self.wd, 10)
        self.base_url = "https://media.ebird.org"

    def _scroll_and_load_more(self, max_attempts=10, timeout_seconds=300):
        """Enhanced scroll and load more functionality with automatic 'Load More' button detection"""
        print("Starting enhanced scroll and load more process...")

        start_time = time.time()
        attempts = 0
        consecutive_no_change = 0
        max_consecutive_no_change = 3

        last_height = self.wd.execute_script("return document.body.scrollHeight")
        last_image_count = len(self._get_current_image_elements())

        while attempts < max_attempts and (time.time() - start_time) < timeout_seconds:
            attempts += 1
            print(f"Scroll attempt {attempts}/{max_attempts}")

            # Scroll to bottom
            self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)

            # Check for and click "Load More" button
            load_more_clicked = self._click_load_more_button()
            if load_more_clicked:
                print("✓ Load More button clicked, waiting for content to load...")
                time.sleep(5)  # Wait longer after clicking Load More

            # Check if page height changed
            new_height = self.wd.execute_script("return document.body.scrollHeight")
            current_image_count = len(self._get_current_image_elements())

            print(f"Height: {last_height} → {new_height}, Images: {last_image_count} → {current_image_count}")

            # If no change in height and image count, increment counter
            if new_height == last_height and current_image_count == last_image_count:
                consecutive_no_change += 1
                print(f"No change detected ({consecutive_no_change}/{max_consecutive_no_change})")

                if consecutive_no_change >= max_consecutive_no_change:
                    print("No more content loading, stopping scroll process")
                    break
            else:
                consecutive_no_change = 0  # Reset counter if there was a change

            last_height = new_height
            last_image_count = current_image_count

            # Additional wait for content to stabilize
            time.sleep(1)

        total_time = time.time() - start_time
        final_image_count = len(self._get_current_image_elements())
        print(f"Scroll process completed in {total_time:.1f}s. Final image count: {final_image_count}")

    def _get_current_image_elements(self):
        """Get current count of image elements on the page"""
        try:
            selectors = [
                'img[src*="macaulaylibrary.org"]',
                'img[src*="cdn.download.ams.birds.cornell.edu"]',
                'img[data-src*="macaulaylibrary.org"]',
                'img[data-src*="cdn.download.ams.birds.cornell.edu"]',
                '.MediaCard img',
                '.media-card img',
                '.photo-card img'
            ]

            all_images = []
            for selector in selectors:
                try:
                    images = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    all_images.extend(images)
                except:
                    continue

            # Remove duplicates based on src attribute
            unique_images = []
            seen_srcs = set()
            for img in all_images:
                src = img.get_attribute('src') or img.get_attribute('data-src')
                if src and src not in seen_srcs:
                    unique_images.append(img)
                    seen_srcs.add(src)

            return unique_images
        except Exception as e:
            print(f"Error getting current image elements: {e}")
            return []

    def _click_load_more_button(self):
        """Detect and click 'Load More' button if available"""
        load_more_selectors = [
            'button[data-testid="load-more"]',
            'button:contains("Load More")',
            'button:contains("Show More")',
            'button:contains("More")',
            '.load-more-button',
            '.show-more-button',
            'button[class*="load"]',
            'button[class*="more"]',
            'a[href*="offset"]',  # Pagination links
            '.pagination a:last-child',
            'button[aria-label*="more"]',
            'button[aria-label*="load"]'
        ]

        for selector in load_more_selectors:
            try:
                # Handle :contains() pseudo-selector manually
                if ':contains(' in selector:
                    text_to_find = selector.split(':contains("')[1].split('")')[0]
                    buttons = self.wd.find_elements(By.TAG_NAME, 'button')
                    for button in buttons:
                        if text_to_find.lower() in button.text.lower():
                            if button.is_displayed() and button.is_enabled():
                                print(f"Found and clicking Load More button: '{button.text}'")
                                self.wd.execute_script("arguments[0].scrollIntoView(true);", button)
                                time.sleep(1)
                                button.click()
                                return True
                else:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            print(f"Found and clicking Load More element with selector: {selector}")
                            self.wd.execute_script("arguments[0].scrollIntoView(true);", element)
                            time.sleep(1)
                            element.click()
                            return True
            except Exception as e:
                continue

        return False

    def _get_clickable_images_from_page(self):
        """Cari semua gambar yang bisa diklik untuk melihat versi penuh"""
        clickable_images = []

        try:
            # Tunggu sampai halaman dimuat
            time.sleep(3)

            # Cari elemen gambar yang bisa diklik (biasanya dalam link atau card)
            selectors = [
                "a[href*='/catalog/'] img",  # Gambar dalam link catalog
                ".MediaCard img",            # Gambar dalam MediaCard
                ".MediaThumbnail img",       # Gambar thumbnail
                "[data-testid='media-card'] img",  # Gambar dalam media card
                "img[src*='macaulaylibrary.org']",  # Gambar langsung
                "img[src*='cdn.download.ams.birds.cornell.edu']"  # Gambar CDN
            ]

            for selector in selectors:
                try:
                    images = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    print(f"Found {len(images)} clickable images with selector: {selector}")

                    for img in images:
                        # Cek apakah gambar bisa diklik (ada parent link atau clickable)
                        parent_link = None
                        try:
                            # Cari parent link
                            parent_link = img.find_element(By.XPATH, "./ancestor::a[1]")
                        except:
                            # Jika tidak ada parent link, coba klik gambar langsung
                            pass

                        clickable_images.append({
                            'element': img,
                            'parent_link': parent_link,
                            'src': img.get_attribute('src') or img.get_attribute('data-src')
                        })

                except Exception as e:
                    print(f"Error with selector {selector}: {e}")
                    continue

            print(f"Total clickable images found: {len(clickable_images)}")
            return clickable_images

        except Exception as e:
            print(f"Error finding clickable images: {e}")
            return []

    def _click_and_get_full_image(self, clickable_img, index, output_dir=None):
        """Enhanced click and get full image with better error handling"""
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                print(f"🖱️ Clicking image {index + 1} (attempt {retry_count + 1}/{max_retries})")

                # Scroll element into view with better positioning
                self.wd.execute_script("""
                    arguments[0].scrollIntoView({
                        behavior: 'smooth',
                        block: 'center',
                        inline: 'center'
                    });
                """, clickable_img['element'])
                time.sleep(2)

                # Wait for element to be clickable
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC

                try:
                    if clickable_img['parent_link']:
                        print(f"🔗 Clicking parent link for image {index + 1}")
                        WebDriverWait(self.wd, 10).until(
                            EC.element_to_be_clickable(clickable_img['parent_link'])
                        )
                        clickable_img['parent_link'].click()
                    else:
                        print(f"🖼️ Clicking image {index + 1} directly")
                        WebDriverWait(self.wd, 10).until(
                            EC.element_to_be_clickable(clickable_img['element'])
                        )
                        clickable_img['element'].click()
                except Exception as click_error:
                    print(f"⚠️ Click failed, trying JavaScript click: {click_error}")
                    # Fallback to JavaScript click
                    element_to_click = clickable_img['parent_link'] or clickable_img['element']
                    self.wd.execute_script("arguments[0].click();", element_to_click)

                # Wait for content to load
                time.sleep(4)

                # Try to find full resolution image first
                full_image_url = self._find_full_resolution_image()

                if full_image_url:
                    print(f"✅ Found full resolution URL: {full_image_url[:80]}...")
                    return full_image_url
                else:
                    # Take enhanced screenshot as fallback
                    print(f"📸 No URL found, taking enhanced screenshot for image {index + 1}")
                    screenshot_path = self._take_screenshot(index, output_dir)
                    if screenshot_path:
                        return screenshot_path
                    else:
                        print(f"❌ Screenshot also failed for image {index + 1}")
                        return None

            except Exception as e:
                retry_count += 1
                error_msg = f"Error clicking image {index + 1} (attempt {retry_count}): {e}"
                print(f"⚠️ {error_msg}")

                if retry_count < max_retries:
                    print(f"🔄 Retrying in 2 seconds...")
                    time.sleep(2)
                    # Try to return to main page before retry
                    self._return_to_main_page()
                else:
                    print(f"❌ All attempts failed for image {index + 1}")
                    return None
            finally:
                # Always try to return to main page
                self._return_to_main_page()

        return None

    def _find_full_resolution_image(self):
        """Enhanced detection untuk gambar resolusi penuh - mencari URL terbaik"""
        try:
            print(f"🔍 Searching for FULL RESOLUTION image URL...")

            # Wait for image to fully load
            time.sleep(3)

            # Ultra high-priority selectors untuk URL resolusi maksimal
            ultra_high_res_selectors = [
                # Modal dan viewer images (prioritas tertinggi)
                ".modal img[src*='macaulaylibrary.org']:not([src*='thumbnail']):not([src*='small'])",
                ".lightbox img[src*='macaulaylibrary.org']:not([src*='thumbnail']):not([src*='small'])",
                ".fullscreen img[src*='macaulaylibrary.org']:not([src*='thumbnail']):not([src*='small'])",

                # eBird specific viewers
                ".MediaDisplay img[src*='macaulaylibrary.org']",
                ".MediaViewer img[src*='macaulaylibrary.org']",
                ".FullscreenImage img[src*='macaulaylibrary.org']",
                "[data-testid='media-display'] img[src*='macaulaylibrary.org']",

                # Images dengan indikator resolusi tinggi
                "img[src*='macaulaylibrary.org'][src*='original']",
                "img[src*='macaulaylibrary.org'][src*='full']",
                "img[src*='macaulaylibrary.org'][src*='xlarge']",
                "img[src*='macaulaylibrary.org'][src*='2400']",
                "img[src*='macaulaylibrary.org'][src*='1920']",

                # Cornell CDN high-res
                "img[src*='cdn.download.ams.birds.cornell.edu']:not([src*='thumbnail'])",

                # General high-quality indicators
                "img[style*='max-width'][src*='macaulaylibrary.org']",
                "img[alt*='photo'][src*='macaulaylibrary.org']",

                # Fallback ke semua eBird images
                "img[src*='macaulaylibrary.org']"
            ]

            best_url = None
            best_score = 0

            for selector in ultra_high_res_selectors:
                try:
                    images = self.wd.find_elements(By.CSS_SELECTOR, selector)

                    for img in images:
                        if img.is_displayed():
                            img_url = img.get_attribute('src')

                            if img_url and any(domain in img_url for domain in ['macaulaylibrary.org', 'cdn.download.ams.birds.cornell.edu']):
                                # Calculate URL quality score
                                score = self._calculate_url_quality_score(img_url, img)

                                if score > best_score:
                                    best_url = img_url
                                    best_score = score
                                    print(f"🎯 Found better URL (score: {score}): {img_url[:80]}...")

                except Exception as e:
                    continue

            if best_url:
                # Convert to absolute highest resolution
                ultra_high_res_url = self._convert_to_ultra_high_res(best_url)
                print(f"✅ BEST RESOLUTION URL found (score: {best_score})")
                return ultra_high_res_url
            else:
                print(f"⚠️ No high-resolution URL found")
                return None

        except Exception as e:
            print(f"❌ Error finding full resolution image: {e}")
            return None

    def _calculate_url_quality_score(self, url, element):
        """Calculate quality score for an image URL"""
        try:
            score = 0
            url_lower = url.lower()

            # URL quality indicators (higher score = better quality)
            if 'original' in url_lower:
                score += 1000
            elif 'full' in url_lower:
                score += 800
            elif 'xlarge' in url_lower:
                score += 600
            elif 'large' in url_lower:
                score += 400
            elif '2400' in url or '1920' in url:
                score += 500

            # Penalties for low quality indicators
            if 'thumbnail' in url_lower or 'thumb' in url_lower:
                score -= 800
            elif 'small' in url_lower:
                score -= 400
            elif 'medium' in url_lower:
                score -= 200

            # Element size bonus
            try:
                size = element.size
                area = size['width'] * size['height']
                score += area / 1000  # Normalize area to score
            except:
                pass

            # Context bonus (modal/viewer context is better)
            try:
                parent_classes = element.find_element(By.XPATH, './..').get_attribute('class') or ''
                if any(cls in parent_classes.lower() for cls in ['modal', 'viewer', 'fullscreen', 'lightbox']):
                    score += 300
            except:
                pass

            return score

        except:
            return 0

    def _convert_to_ultra_high_res(self, img_url):
        """Convert URL to absolute maximum resolution"""
        if not img_url:
            return None

        try:
            print(f"🚀 Converting to ULTRA HIGH RESOLUTION...")
            original_url = img_url

            if 'macaulaylibrary.org' in img_url:
                # Remove all size parameters
                base_url = img_url.split('?')[0]

                # Try the absolute best resolution parameters
                ultra_params = [
                    '?size=original&quality=100',
                    '?width=5000&height=4000&quality=100', 
                    '?size=full&quality=100',
                    '?size=xlarge&quality=100',
                    '?width=2400&height=1600&quality=100',
                    '?w=2400&h=1600&q=100',
                    '?size=2400&quality=100',
                    '?original=true',
                    ''  # Sometimes no parameters gives original
                ]

                # Test each parameter and pick the best
                for param in ultra_params:
                    test_url = base_url + param
                    if self._verify_high_quality_url(test_url):
                        print(f"✅ Ultra high-res URL verified: {param}")
                        return test_url

                # If no verification possible, use the best parameter
                return base_url + '?size=original&quality=100'

            elif 'cdn.download.ams.birds.cornell.edu' in img_url:
                # Cornell CDN optimization
                img_url = img_url.replace('/thumbnails/', '/catalog/')
                img_url = img_url.replace('_thumbnail', '')
                img_url = img_url.replace('_small', '')
                img_url = img_url.replace('_medium', '')
                img_url = img_url.replace('_large', '')

                # Add quality parameters
                if '?' not in img_url:
                    img_url += '?quality=100&size=original'

                return img_url

            return img_url

        except Exception as e:
            print(f"❌ Error converting to ultra high-res: {e}")
            return original_url

    def _verify_high_quality_url(self, url):
        """Verify if URL returns high quality image"""
        try:
            import requests
            response = requests.head(url, timeout=3)
            if response.status_code == 200:
                content_length = response.headers.get('content-length')
                if content_length:
                    size_kb = int(content_length) / 1024
                    # High quality images should be at least 100KB
                    return size_kb > 100
        except:
            pass
        return False

    def _take_screenshot(self, index, output_dir=None):
        """Ultra high-quality screenshot functionality - mimics manual screenshot behavior"""
        try:
            print(f"📸 Taking ULTRA HIGH-QUALITY screenshot for image {index + 1}...")

            # Set output directory
            if output_dir is None:
                output_dir = os.getcwd()

            # Wait for full loading and animations
            time.sleep(3)

            # Set browser to maximum quality mode
            self._optimize_browser_for_screenshots()

            # Hide all UI overlays and distractions
            self._hide_ui_overlays()

            # Try to maximize the image display first
            self._maximize_image_display()

            # Find the absolute best image element
            screenshot_element = self._find_ultra_high_res_target()

            if screenshot_element:
                # Get element dimensions and optimize
                element_size = screenshot_element.size
                print(f"🎯 Target element size: {element_size['width']}x{element_size['height']} pixels")

                # Scroll and center perfectly
                self.wd.execute_script("""
                    arguments[0].scrollIntoView({
                        behavior: 'smooth',
                        block: 'center',
                        inline: 'center'
                    });

                    // Remove any transforms or filters that might reduce quality
                    arguments[0].style.transform = 'none';
                    arguments[0].style.filter = 'none';
                    arguments[0].style.opacity = '1';

                    // Ensure maximum quality display
                    arguments[0].style.imageRendering = 'high-quality';
                    arguments[0].style.imageRendering = '-webkit-optimize-contrast';
                """, screenshot_element)

                time.sleep(2)  # Wait for rendering

                # Take ultra high-quality screenshot
                screenshot_filename = f"ebird_ultra_hq_{index + 1:03d}.png"
                screenshot_path = os.path.join(output_dir, screenshot_filename)

                # Try multiple screenshot methods for best quality
                success = self._take_ultra_quality_screenshot(screenshot_element, screenshot_path)

                if success:
                    file_size = os.path.getsize(screenshot_path)
                    print(f"✅ ULTRA HIGH-QUALITY screenshot saved: {screenshot_filename}")
                    print(f"📊 File size: {file_size/1024:.1f} KB")
                    return screenshot_path
                else:
                    print("🔄 Ultra quality failed, trying enhanced fallback...")
                    return self._take_enhanced_fallback_screenshot(index, output_dir)

            else:
                print("🔄 No ultra-high-res target found, trying enhanced fallback...")
                return self._take_enhanced_fallback_screenshot(index, output_dir)

        except Exception as e:
            print(f"❌ Error in ultra high-quality screenshot: {e}")
            return self._take_enhanced_fallback_screenshot(index, output_dir)

    def _optimize_browser_for_screenshots(self):
        """Optimize browser settings for maximum screenshot quality"""
        try:
            # Set maximum zoom for better quality
            self.wd.execute_script("document.body.style.zoom = '1.0';")

            # Disable any image compression or optimization
            self.wd.execute_script("""
                // Disable image optimization
                var style = document.createElement('style');
                style.textContent = `
                    img {
                        image-rendering: -webkit-optimize-contrast !important;
                        image-rendering: high-quality !important;
                        image-rendering: crisp-edges !important;
                        -ms-interpolation-mode: bicubic !important;
                    }
                `;
                document.head.appendChild(style);
            """)

            # Wait for styles to apply
            time.sleep(1)

        except Exception as e:
            print(f"⚠️ Warning: Could not optimize browser for screenshots: {e}")

    def _maximize_image_display(self):
        """Try to maximize the image display like manual viewing"""
        try:
            # Look for fullscreen or maximize buttons
            maximize_selectors = [
                'button[aria-label*="fullscreen"]',
                'button[aria-label*="maximize"]',
                'button[title*="fullscreen"]',
                'button[title*="maximize"]',
                '.fullscreen-button',
                '.maximize-button',
                '[data-testid*="fullscreen"]',
                '[data-testid*="maximize"]'
            ]

            for selector in maximize_selectors:
                try:
                    button = self.wd.find_element(By.CSS_SELECTOR, selector)
                    if button.is_displayed() and button.is_enabled():
                        print(f"🔍 Found maximize button, clicking...")
                        button.click()
                        time.sleep(2)
                        return True
                except:
                    continue

            # Try keyboard shortcut for fullscreen
            try:
                from selenium.webdriver.common.keys import Keys
                body = self.wd.find_element(By.TAG_NAME, 'body')
                body.send_keys(Keys.F11)  # Try F11 for fullscreen
                time.sleep(1)
            except:
                pass

        except Exception as e:
            print(f"⚠️ Could not maximize image display: {e}")

        return False

    def _find_ultra_high_res_target(self):
        """Find the absolute best, highest resolution image element"""
        try:
            # Ultra high priority selectors for maximum quality
            ultra_selectors = [
                # Full-size modal images (highest priority)
                '.modal img[src*="macaulaylibrary.org"]:not([src*="thumbnail"]):not([src*="small"])',
                '.lightbox img[src*="macaulaylibrary.org"]:not([src*="thumbnail"]):not([src*="small"])',
                '.fullscreen img[src*="macaulaylibrary.org"]:not([src*="thumbnail"]):not([src*="small"])',

                # eBird specific high-res viewers
                '.MediaDisplay img[src*="macaulaylibrary.org"]',
                '.MediaViewer img[src*="macaulaylibrary.org"]',
                '.FullscreenImage img[src*="macaulaylibrary.org"]',
                '[data-testid="media-display"] img[src*="macaulaylibrary.org"]',

                # Large display images with size indicators
                'img[src*="macaulaylibrary.org"][src*="original"]',
                'img[src*="macaulaylibrary.org"][src*="full"]',
                'img[src*="macaulaylibrary.org"][src*="xlarge"]',
                'img[src*="macaulaylibrary.org"][src*="2400"]',
                'img[src*="macaulaylibrary.org"][src*="1920"]',

                # Cornell CDN high-res images
                'img[src*="cdn.download.ams.birds.cornell.edu"]:not([src*="thumbnail"])',

                # Any large eBird image
                'img[src*="macaulaylibrary.org"]'
            ]

            best_element = None
            best_score = 0

            for selector in ultra_selectors:
                try:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            # Calculate quality score
                            score = self._calculate_image_quality_score(element)

                            if score > best_score:
                                best_element = element
                                best_score = score

                except Exception as e:
                    continue

            if best_element:
                print(f"🎯 Found ULTRA HIGH-RES target with quality score: {best_score}")
                return best_element
            else:
                print("⚠️ No ultra high-res target found")
                return None

        except Exception as e:
            print(f"❌ Error finding ultra high-res target: {e}")
            return None

    def _calculate_image_quality_score(self, element):
        """Calculate quality score for an image element"""
        try:
            score = 0

            # Size score (larger is better)
            size = element.size
            area = size['width'] * size['height']
            score += area / 1000  # Normalize

            # URL quality indicators
            src = element.get_attribute('src') or ''
            if 'original' in src.lower():
                score += 1000
            elif 'full' in src.lower():
                score += 800
            elif 'xlarge' in src.lower():
                score += 600
            elif 'large' in src.lower():
                score += 400
            elif '2400' in src or '1920' in src:
                score += 500

            # Penalty for thumbnails
            if 'thumbnail' in src.lower() or 'thumb' in src.lower():
                score -= 500
            if 'small' in src.lower():
                score -= 300

            # Bonus for modal/viewer context
            parent_classes = element.find_element(By.XPATH, './..').get_attribute('class') or ''
            if any(cls in parent_classes.lower() for cls in ['modal', 'viewer', 'fullscreen', 'lightbox']):
                score += 200

            return score

        except:
            return 0

    def _take_ultra_quality_screenshot(self, element, screenshot_path):
        """Take the highest quality screenshot possible"""
        try:
            # Method 1: Element screenshot (usually highest quality)
            element.screenshot(screenshot_path)

            # Verify quality
            if os.path.exists(screenshot_path):
                file_size = os.path.getsize(screenshot_path)
                if file_size > 10000:  # At least 10KB for decent quality
                    return True
                else:
                    print(f"⚠️ Screenshot too small ({file_size} bytes), trying alternative...")

            # Method 2: Full page screenshot as fallback
            self.wd.save_screenshot(screenshot_path)

            if os.path.exists(screenshot_path) and os.path.getsize(screenshot_path) > 10000:
                return True

            return False

        except Exception as e:
            print(f"❌ Error taking ultra quality screenshot: {e}")
            return False

    def _hide_ui_overlays(self):
        """Hide UI overlays and elements that might interfere with screenshots"""
        try:
            # Common overlay selectors to hide
            overlay_selectors = [
                '.overlay',
                '.modal-overlay',
                '.popup-overlay',
                '.tooltip',
                '.dropdown-menu',
                'nav',
                'header',
                '.navigation',
                '.toolbar',
                '.controls',
                '.ui-controls',
                '[class*="overlay"]',
                '[class*="popup"]',
                '[class*="tooltip"]'
            ]

            hidden_elements = []
            for selector in overlay_selectors:
                try:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            # Store original display style
                            original_style = element.get_attribute('style')
                            # Hide element
                            self.wd.execute_script("arguments[0].style.display = 'none';", element)
                            hidden_elements.append((element, original_style))
                except:
                    continue

            # Store hidden elements for potential restoration
            self._hidden_elements = hidden_elements

        except Exception as e:
            print(f"Warning: Could not hide UI overlays: {e}")

    def _find_best_screenshot_target(self):
        """Find the best image element for screenshot"""
        try:
            # Priority-ordered selectors for finding the main image
            image_selectors = [
                # Modal/viewer images (highest priority)
                '.modal img[src*="macaulaylibrary.org"]',
                '.viewer img[src*="macaulaylibrary.org"]',
                '.lightbox img[src*="macaulaylibrary.org"]',
                '.fullscreen img[src*="macaulaylibrary.org"]',
                '.MediaDisplay img',
                '.MediaViewer img',
                '.FullscreenImage img',
                '[data-testid="media-display"] img',

                # Large display images
                'img[class*="large"][src*="macaulaylibrary.org"]',
                'img[class*="full"][src*="macaulaylibrary.org"]',
                'img[class*="detail"][src*="macaulaylibrary.org"]',

                # General eBird images
                'img[src*="macaulaylibrary.org"]',
                'img[src*="cdn.download.ams.birds.cornell.edu"]',

                # Fallback to any large visible image
                'img[width][height]'
            ]

            best_element = None
            best_size = 0

            for selector in image_selectors:
                try:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            # Calculate element size
                            size = element.size
                            element_area = size['width'] * size['height']

                            # Prefer larger images
                            if element_area > best_size:
                                best_element = element
                                best_size = element_area

                except Exception as e:
                    continue

            if best_element:
                print(f"Found best screenshot target: {best_size} pixels area")
                return best_element
            else:
                print("No suitable screenshot target found")
                return None

        except Exception as e:
            print(f"Error finding screenshot target: {e}")
            return None

    def _take_enhanced_fallback_screenshot(self, index, output_dir):
        """Enhanced fallback screenshot with multiple strategies"""
        try:
            print(f"🔄 Taking enhanced fallback screenshot for image {index + 1}...")

            # Strategy 1: Try to find any large image on the page
            large_images = self._find_large_images_on_page()
            if large_images:
                for img in large_images[:3]:  # Try top 3 largest images
                    try:
                        screenshot_filename = f"ebird_fallback_{index + 1:03d}.png"
                        screenshot_path = os.path.join(output_dir, screenshot_filename)

                        # Scroll to image and take screenshot
                        self.wd.execute_script("arguments[0].scrollIntoView(true);", img)
                        time.sleep(1)

                        img.screenshot(screenshot_path)

                        if os.path.exists(screenshot_path) and os.path.getsize(screenshot_path) > 5000:
                            print(f"✅ Enhanced fallback screenshot saved: {screenshot_filename}")
                            return screenshot_path
                    except:
                        continue

            # Strategy 2: Full page screenshot with optimization
            screenshot_filename = f"ebird_fullpage_{index + 1:03d}.png"
            screenshot_path = os.path.join(output_dir, screenshot_filename)

            # Optimize page for screenshot
            self.wd.execute_script("""
                // Hide navigation and UI elements
                var elements = document.querySelectorAll('nav, header, .navigation, .toolbar');
                elements.forEach(el => el.style.display = 'none');

                // Maximize content area
                document.body.style.margin = '0';
                document.body.style.padding = '0';
            """)

            time.sleep(1)
            self.wd.save_screenshot(screenshot_path)

            if os.path.exists(screenshot_path) and os.path.getsize(screenshot_path) > 1000:
                print(f"✅ Full page fallback screenshot saved: {screenshot_filename}")
                return screenshot_path
            else:
                print(f"❌ Failed to create any fallback screenshot")
                return None

        except Exception as e:
            print(f"❌ Error taking enhanced fallback screenshot: {e}")
            return None

    def _find_large_images_on_page(self):
        """Find all large images on the current page"""
        try:
            all_images = self.wd.find_elements(By.TAG_NAME, 'img')
            large_images = []

            for img in all_images:
                try:
                    if img.is_displayed():
                        size = img.size
                        area = size['width'] * size['height']

                        # Consider images larger than 100x100 pixels
                        if area > 10000:  # 100x100 = 10,000 pixels
                            large_images.append((img, area))
                except:
                    continue

            # Sort by area (largest first)
            large_images.sort(key=lambda x: x[1], reverse=True)

            # Return just the image elements
            return [img for img, area in large_images]

        except Exception as e:
            print(f"❌ Error finding large images: {e}")
            return []

    def _return_to_main_page(self):
        """Kembali ke halaman utama"""
        try:
            # Jika ada multiple windows/tabs, tutup yang baru dan kembali ke utama
            if len(self.wd.window_handles) > 1:
                self.wd.close()
                self.wd.switch_to.window(self.wd.window_handles[0])
            else:
                # Jika modal/overlay, coba tutup dengan ESC atau tombol close
                try:
                    # Coba tekan ESC
                    from selenium.webdriver.common.keys import Keys
                    self.wd.find_element(By.TAG_NAME, 'body').send_keys(Keys.ESCAPE)
                    time.sleep(1)
                except:
                    pass

                # Coba cari tombol close
                close_selectors = [
                    ".close", ".modal-close", ".lightbox-close",
                    "[aria-label='Close']", "[title='Close']",
                    ".fa-times", ".fa-close", ".icon-close"
                ]

                for selector in close_selectors:
                    try:
                        close_btn = self.wd.find_element(By.CSS_SELECTOR, selector)
                        close_btn.click()
                        time.sleep(1)
                        break
                    except:
                        continue

        except Exception as e:
            print(f"Error returning to main page: {e}")

    def _get_image_urls_from_page(self):
        """Ekstrak URL gambar dengan metode klik dan lihat (DEPRECATED - gunakan _click_and_download_images)"""
        # Method lama untuk backward compatibility
        return self._click_and_download_images(max_images=50, download_method='url_only')

    def _convert_to_high_res(self, img_url):
        """Enhanced URL conversion untuk mendapatkan resolusi maksimal"""
        if not img_url:
            return None

        try:
            original_url = img_url
            print(f"🔍 Converting URL to highest resolution: {img_url[:100]}...")

            if 'macaulaylibrary.org' in img_url:
                # Strategi untuk Macaulay Library - coba berbagai parameter resolusi tinggi
                base_url = img_url.split('?')[0]  # Hapus semua parameter

                # Coba berbagai parameter untuk resolusi maksimal
                high_res_params = [
                    '?size=original',      # Resolusi asli
                    '?size=full',          # Full size
                    '?size=xlarge',        # Extra large
                    '?size=2400',          # Specific size
                    '?size=1920',          # HD size
                    '?width=2400',         # Width parameter
                    '?w=2400&h=1600',      # Width & height
                    ''                     # No parameters (sometimes gives full size)
                ]

                # Test each parameter to find the largest image
                for param in high_res_params:
                    test_url = base_url + param
                    if self._test_image_url_size(test_url):
                        img_url = test_url
                        break

            elif 'cdn.download.ams.birds.cornell.edu' in img_url:
                # Strategi untuk Cornell CDN
                img_url = img_url.replace('/thumbnails/', '/catalog/')
                img_url = img_url.replace('_thumbnail', '')
                img_url = img_url.replace('_small', '')
                img_url = img_url.replace('_medium', '')
                img_url = img_url.replace('_large', '')  # Remove size restrictions

                # Coba tambahkan parameter resolusi tinggi
                if '?' not in img_url:
                    img_url += '?size=original'

            # Coba pattern lain untuk eBird
            elif 'ebird.org' in img_url or 'birds.cornell.edu' in img_url:
                # Remove size restrictions
                img_url = img_url.replace('_s.', '_o.')  # small to original
                img_url = img_url.replace('_m.', '_o.')  # medium to original
                img_url = img_url.replace('_l.', '_o.')  # large to original
                img_url = img_url.replace('_thumb.', '_o.')  # thumbnail to original

                # Add high resolution parameters
                if '?' in img_url:
                    img_url += '&quality=100&size=original'
                else:
                    img_url += '?quality=100&size=original'

            if img_url != original_url:
                print(f"✅ URL converted for higher resolution")
            else:
                print(f"⚠️ No conversion applied, using original URL")

            return img_url

        except Exception as e:
            print(f"Error converting URL to high res: {e}")
            return original_url

    def _test_image_url_size(self, url):
        """Test if an image URL returns a larger file"""
        try:
            import requests
            response = requests.head(url, timeout=5)
            if response.status_code == 200:
                content_length = response.headers.get('content-length')
                if content_length and int(content_length) > 50000:  # > 50KB indicates good quality
                    return True
        except:
            pass
        return False

    def _get_high_res_image_from_detail(self, detail_url):
        """Ambil URL gambar resolusi tinggi dari halaman detail"""
        try:
            # Buka tab baru untuk halaman detail
            self.wd.execute_script("window.open('');")
            self.wd.switch_to.window(self.wd.window_handles[1])

            self.wd.get(detail_url)
            time.sleep(3)

            # Cari gambar resolusi tinggi dengan berbagai selector
            selectors = [
                "img[src*='macaulaylibrary.org']:not([src*='thumbnail'])",
                "img[src*='cdn.download.ams.birds.cornell.edu']:not([src*='thumbnail'])",
                ".MediaDisplay img",
                ".MediaViewer img",
                "[data-testid='media-display'] img",
                "img[alt*='photo']",
                "img[alt*='image']"
            ]

            img_url = None
            for selector in selectors:
                try:
                    high_res_img = self.wd.find_element(By.CSS_SELECTOR, selector)
                    img_url = high_res_img.get_attribute('src')
                    if img_url and any(domain in img_url for domain in ['macaulaylibrary.org', 'cdn.download.ams.birds.cornell.edu']):
                        break
                except:
                    continue

            # Tutup tab dan kembali ke tab utama
            self.wd.close()
            self.wd.switch_to.window(self.wd.window_handles[0])

            return self._convert_to_high_res(img_url) if img_url else None

        except Exception as e:
            print(f"Error getting high res image from {detail_url}: {e}")
            # Jika ada error, tutup tab dan kembali ke tab utama
            try:
                if len(self.wd.window_handles) > 1:
                    self.wd.close()
                    self.wd.switch_to.window(self.wd.window_handles[0])
            except:
                pass
            return None

    def _download_image(self, img_url, filename, output_dir):
        """Download gambar dari URL"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(img_url, headers=headers, stream=True)
            response.raise_for_status()

            filepath = os.path.join(output_dir, filename)

            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            return True

        except Exception as e:
            print(f"Error downloading {img_url}: {e}")
            return False

    def _click_and_download_images(self, max_images=50, download_method='both'):
        """Method utama: klik gambar, lihat versi penuh, kemudian download/screenshot"""
        downloaded_items = []

        try:
            # Cari semua gambar yang bisa diklik
            clickable_images = self._get_clickable_images_from_page()

            if not clickable_images:
                print("No clickable images found")
                return []

            # Batasi jumlah gambar
            if max_images and len(clickable_images) > max_images:
                clickable_images = clickable_images[:max_images]

            print(f"Processing {len(clickable_images)} images...")

            for i, clickable_img in enumerate(tqdm.tqdm(clickable_images, desc="Processing images")):
                try:
                    # Klik dan dapatkan gambar penuh
                    result = self._click_and_get_full_image(clickable_img, i)

                    if result:
                        downloaded_items.append(result)

                    # Jeda untuk menghindari rate limiting
                    time.sleep(1)

                except Exception as e:
                    print(f"Error processing image {i+1}: {e}")
                    continue

            return downloaded_items

        except Exception as e:
            print(f"Error in click and download process: {e}")
            return []

    def scrape_ebird(self, ebird_url, output_dir, max_images=50, method='click_and_view', timeout_minutes=30):
        """Enhanced eBird scraper with robust error handling and bulk processing"""
        print("=" * 60)
        print("🦅 ENHANCED EBIRD SCRAPER STARTING")
        print("=" * 60)
        print(f"📍 URL: {ebird_url}")
        print(f"🎯 Method: {method}")
        print(f"📁 Output: {output_dir}")
        print(f"🔢 Max images: {max_images}")
        print(f"⏱️ Timeout: {timeout_minutes} minutes")
        print("=" * 60)

        start_time = time.time()
        timeout_seconds = timeout_minutes * 60

        # Statistics tracking
        stats = {
            'attempted': 0,
            'successful_downloads': 0,
            'successful_screenshots': 0,
            'failed': 0,
            'errors': []
        }

        try:
            # Create output directory
            os.makedirs(output_dir, exist_ok=True)
            print(f"✓ Output directory created: {output_dir}")

            # Open eBird page with error handling
            print("\n🌐 Loading eBird page...")
            try:
                self.wd.get(ebird_url)
                time.sleep(3)
                print("✓ Page loaded successfully")
            except Exception as e:
                print(f"✗ Failed to load page: {e}")
                return self._return_error_stats(stats, f"Failed to load page: {e}")

            # Enhanced scroll and load more with progress feedback
            print("\n📜 Loading all available images...")
            try:
                self._scroll_and_load_more(timeout_seconds=min(300, timeout_seconds//2))
                print("✓ Page scrolling and loading completed")
            except Exception as e:
                print(f"⚠️ Warning during page loading: {e}")
                stats['errors'].append(f"Page loading warning: {e}")

            downloaded_count = 0

            if method == 'click_and_view':
                downloaded_count = self._process_click_and_view_method(
                    output_dir, max_images, stats, timeout_seconds, start_time
                )

            # Fallback to direct URL method if needed
            if (method == 'direct_url' or downloaded_count == 0) and (time.time() - start_time) < timeout_seconds:
                print("\n🔄 Using direct URL method as fallback...")
                fallback_count = self._process_direct_url_method(
                    output_dir, max_images, stats, timeout_seconds, start_time
                )
                downloaded_count += fallback_count

        except Exception as e:
            print(f"\n💥 Critical error in scraping process: {e}")
            stats['errors'].append(f"Critical error: {e}")

        finally:
            # Always quit browser
            try:
                self.wd.quit()
            except:
                pass

        # Print final statistics
        self._print_final_stats(stats, start_time, output_dir)
        return stats['successful_downloads'] + stats['successful_screenshots']

    def _process_click_and_view_method(self, output_dir, max_images, stats, timeout_seconds, start_time):
        """Process images using click and view method with enhanced error handling"""
        print("\n🖱️ Using CLICK AND VIEW method...")

        try:
            # Find clickable images
            clickable_images = self._get_clickable_images_from_page()

            if not clickable_images:
                print("⚠️ No clickable images found")
                return 0

            # Limit images if specified
            if max_images and len(clickable_images) > max_images:
                clickable_images = clickable_images[:max_images]
                print(f"📊 Limited to {max_images} images (found {len(clickable_images)} total)")

            print(f"🎯 Processing {len(clickable_images)} images...")

            successful_count = 0

            for i, clickable_img in enumerate(tqdm.tqdm(clickable_images, desc="🖼️ Processing images")):
                # Check timeout
                if (time.time() - start_time) > timeout_seconds:
                    print(f"\n⏰ Timeout reached, stopping at image {i+1}")
                    break

                stats['attempted'] += 1

                try:
                    print(f"\n📸 Processing image {i+1}/{len(clickable_images)}")

                    # Click and get full image
                    result = self._click_and_get_full_image(clickable_img, i, output_dir)

                    if result:
                        if result.endswith('.png'):
                            # Screenshot result
                            import shutil
                            new_path = os.path.join(output_dir, os.path.basename(result))
                            if os.path.exists(result):
                                shutil.move(result, new_path)
                                stats['successful_screenshots'] += 1
                                successful_count += 1
                                print(f"✓ Screenshot saved: {os.path.basename(result)}")
                            else:
                                print(f"⚠️ Screenshot file not found: {result}")
                                stats['failed'] += 1
                        else:
                            # URL result - download image
                            filename = f"ebird_image_{i+1:03d}.jpg"
                            if self._download_image(result, filename, output_dir):
                                stats['successful_downloads'] += 1
                                successful_count += 1
                                print(f"✓ Image downloaded: {filename}")
                            else:
                                stats['failed'] += 1
                                print(f"✗ Failed to download: {filename}")
                    else:
                        stats['failed'] += 1
                        print(f"✗ No result for image {i+1}")

                    # Progress feedback
                    if (i + 1) % 5 == 0:
                        elapsed = time.time() - start_time
                        avg_time = elapsed / (i + 1)
                        remaining = (len(clickable_images) - i - 1) * avg_time
                        print(f"📊 Progress: {i+1}/{len(clickable_images)} | Success: {successful_count} | ETA: {remaining/60:.1f}min")

                    # Rate limiting
                    time.sleep(1)

                except Exception as e:
                    stats['failed'] += 1
                    error_msg = f"Error processing image {i+1}: {e}"
                    print(f"✗ {error_msg}")
                    stats['errors'].append(error_msg)
                    continue

            return successful_count

        except Exception as e:
            error_msg = f"Error in click and view method: {e}"
            print(f"💥 {error_msg}")
            stats['errors'].append(error_msg)
            return 0

    def _process_direct_url_method(self, output_dir, max_images, stats, timeout_seconds, start_time):
        """Process images using direct URL method"""
        try:
            image_urls = self._get_image_urls_old_method()
            print(f"🔗 Found {len(image_urls)} image URLs")

            if not image_urls:
                print("⚠️ No image URLs found")
                return 0

            # Limit images if specified
            if max_images and len(image_urls) > max_images:
                image_urls = image_urls[:max_images]

            successful_count = 0

            for i, img_url in enumerate(tqdm.tqdm(image_urls, desc="⬇️ Downloading images")):
                # Check timeout
                if (time.time() - start_time) > timeout_seconds:
                    print(f"\n⏰ Timeout reached, stopping at image {i+1}")
                    break

                stats['attempted'] += 1
                filename = f"ebird_image_{i+1:03d}.jpg"

                try:
                    if self._download_image(img_url, filename, output_dir):
                        stats['successful_downloads'] += 1
                        successful_count += 1
                        print(f"✓ Downloaded: {filename}")
                    else:
                        stats['failed'] += 1
                        print(f"✗ Failed: {filename}")
                except Exception as e:
                    stats['failed'] += 1
                    error_msg = f"Error downloading {filename}: {e}"
                    print(f"✗ {error_msg}")
                    stats['errors'].append(error_msg)

                time.sleep(0.5)  # Rate limiting

            return successful_count

        except Exception as e:
            error_msg = f"Error in direct URL method: {e}"
            print(f"💥 {error_msg}")
            stats['errors'].append(error_msg)
            return 0

    def _return_error_stats(self, stats, error_message):
        """Return error statistics"""
        stats['errors'].append(error_message)
        self._print_final_stats(stats, time.time(), "N/A")
        return 0

    def _print_final_stats(self, stats, start_time, output_dir):
        """Print comprehensive final statistics"""
        elapsed_time = time.time() - start_time
        total_successful = stats['successful_downloads'] + stats['successful_screenshots']

        print("\n" + "=" * 60)
        print("📊 SCRAPING COMPLETED - FINAL STATISTICS")
        print("=" * 60)
        print(f"⏱️ Total time: {elapsed_time/60:.1f} minutes")
        print(f"🎯 Images attempted: {stats['attempted']}")
        print(f"✅ Total successful: {total_successful}")
        print(f"  📥 Downloads: {stats['successful_downloads']}")
        print(f"  📸 Screenshots: {stats['successful_screenshots']}")
        print(f"❌ Failed: {stats['failed']}")

        if stats['attempted'] > 0:
            success_rate = (total_successful / stats['attempted']) * 100
            print(f"📈 Success rate: {success_rate:.1f}%")

        if total_successful > 0:
            avg_time = elapsed_time / total_successful
            print(f"⚡ Average time per image: {avg_time:.1f} seconds")

        print(f"📁 Output directory: {output_dir}")

        if stats['errors']:
            print(f"\n⚠️ Errors encountered ({len(stats['errors'])}):")
            for i, error in enumerate(stats['errors'][:5], 1):  # Show first 5 errors
                print(f"  {i}. {error}")
            if len(stats['errors']) > 5:
                print(f"  ... and {len(stats['errors']) - 5} more errors")

        print("=" * 60)

    def _get_image_urls_old_method(self):
        """Method lama untuk mengambil URL gambar langsung"""
        image_urls = []

        try:
            # Cari semua elemen gambar dengan berbagai selector
            selectors = [
                "img[src*='macaulaylibrary.org']",
                "img[src*='cdn.download.ams.birds.cornell.edu']",
                "img[data-src*='macaulaylibrary.org']",
                "img[data-src*='cdn.download.ams.birds.cornell.edu']",
                ".MediaCard img",
                ".MediaThumbnail img",
                "[data-testid='media-card'] img"
            ]

            for selector in selectors:
                try:
                    images = self.wd.find_elements(By.CSS_SELECTOR, selector)

                    for img in images:
                        # Coba ambil dari src atau data-src
                        img_url = img.get_attribute('src') or img.get_attribute('data-src')

                        if img_url and any(domain in img_url for domain in ['macaulaylibrary.org', 'cdn.download.ams.birds.cornell.edu']):
                            # Konversi ke URL resolusi tinggi
                            high_res_url = self._convert_to_high_res(img_url)
                            if high_res_url:
                                image_urls.append(high_res_url)

                except Exception as e:
                    continue

            # Hapus duplikat
            unique_urls = list(set(image_urls))
            return unique_urls

        except Exception as e:
            print(f"Error getting image URLs: {e}")
            return []


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Web scraper for Google Images and eBird photos")

    # Tambahkan pilihan mode
    parser.add_argument('--mode', choices=['google', 'ebird'], default='google',
                        help='Pilih mode scraping: google untuk Google Images, ebird untuk eBird')

    # Argumen untuk Google Images (mode lama)
    parser.add_argument('--search', type=str,
                        help='The keyword to search in Google Images')

    parser.add_argument('--min_image_count', type=int, default=1,
                        help='Minimum number of images for Google Images scraping')

    # Argumen untuk eBird
    parser.add_argument('--ebird_url', type=str,
                        help='URL eBird untuk scraping foto (contoh: https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1)')

    parser.add_argument('--max_images', type=int, default=50,
                        help='Maksimum jumlah gambar untuk didownload dari eBird')

    parser.add_argument('--method', choices=['click_and_view', 'direct_url'], default='click_and_view',
                        help='Metode scraping: click_and_view (klik gambar dulu, lihat versi penuh) atau direct_url (ambil URL langsung)')

    parser.add_argument('--timeout', type=int, default=30,
                        help='Timeout dalam menit untuk proses scraping (default: 30 menit)')

    parser.add_argument('--ultra_quality', action='store_true',
                        help='Aktifkan mode ultra high quality untuk resolusi maksimal (lebih lambat tapi kualitas terbaik)')

    # Argumen umum
    parser.add_argument('--out_directory', default=os.getcwd(),
                        help='The full path to the output directory')

    parser.add_argument('--headless', action='store_true',
                        help='Jalankan browser dalam mode headless (tanpa GUI)')

    args = parser.parse_args()

    if args.mode == 'google':
        if not args.search:
            print("Error: --search diperlukan untuk mode Google Images")
            exit(1)

        SEARCH = args.search
        IMAGECOUNT = args.min_image_count
        OUTDIR = args.out_directory

        scraperBot = ScraperBot()
        scraperBot.scrape(search=SEARCH, min_image_count=IMAGECOUNT, directory=OUTDIR)

    elif args.mode == 'ebird':
        if not args.ebird_url:
            print("Error: --ebird_url diperlukan untuk mode eBird")
            print("Contoh: python scraperBot.py --mode ebird --ebird_url 'https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1'")
            exit(1)

        ebird_scraper = EBirdScraperBot(headless=args.headless)

        # Set ultra quality mode if requested
        if args.ultra_quality:
            print("🚀 ULTRA QUALITY MODE ACTIVATED!")
            print("⏱️ This will take longer but produce maximum quality results.")
            ebird_scraper.ultra_quality_mode = True

        ebird_scraper.scrape_ebird(
            ebird_url=args.ebird_url,
            output_dir=args.out_directory,
            max_images=args.max_images,
            method=args.method,
            timeout_minutes=args.timeout
        )