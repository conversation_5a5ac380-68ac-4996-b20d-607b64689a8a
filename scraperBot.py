from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import os
import time
from urllib import request as wget
import tqdm as tqdm
import argparse
import requests
import json
from urllib.parse import urljoin, urlparse
'''

wd = webdriver.Chrome(executable_path=os.path.join(os.getcwd(), 'utils\\chromedriver.exe'))
wd.get('https://google.com')
search = wd.find_element_by_css_selector('input.gLFyf')
search.send_keys('')

'''

class ScraperBot():
    def __init__(self):
        self.wd = webdriver.Chrome(executable_path=os.path.join(os.getcwd(), 'chromedriver.exe'))
        self.urlbase = "https://google.com/search?tbm=isch&q={}"

    def _initiateSession(self, key):
        self.wd.get(self.urlbase.format(key))

    def _acceptCookies(self):
        try:
            self.wd.execute_script("document.getElementsByClassName('USRMqe')[0].style.display = 'none';")
        except:
            pass

    def _endOfPage(self):
        try:
            self.wd.find_element_by_class_name('OuJzKb Yu2Dnd')
            print("no more files")
        except:
            pass

        try:
            self.wd.find_element_by_class_name('mye4qd').click()
            time.sleep(1)
            self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        except:
            self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")


    def _deduplicate(self, listOfurls):
        try:
            inputList = list(set(listOfurls))
            return inputList
        except:
            pass

    def _checkOpenTabs(self):
        browserTabs = self.wd.window_handles
        if len(browserTabs) > 1:
            self.wd.switch_to.window(browserTabs[1])
            self.wd.close()
            self.wd.switch_to.window(browserTabs[0])

    def _getURL(self):
        thumbs = self.wd.find_elements_by_css_selector('img.Q4LuWd')
        urls = []
        for thumbImg in thumbs:
            try:
                thumbImg.click()
                actualImg = self.wd.find_elements_by_css_selector('img.n3VNCb')

                for imageData in actualImg:
                    if 'https' in imageData.get_attribute('src'):
                        urls.append(imageData.get_attribute('src'))

                self._checkOpenTabs()
            except:
                pass
        return urls

    def _totalImages(self, dir):
        count = 0
        for filename in os.listdir(dir):
            if filename.endswith('.jpg'):
                count += 1
            else:
                continue
        return count

    def _downloader(self, data, key, out_dir):
        key = key.replace(" ", "_")
        DIR1 = os.path.join(out_dir, key)

        try:
            os.mkdir(DIR1)
        except:
            pass

        for idx in tqdm.tqdm(range(len(data))):
            filename = "{}-{}.jpg".format(key, idx)
            PATH = os.path.join(DIR1, '{}'.format(filename))

            try:
                print("downloading next batch")
                wget.urlretrieve(str(data[idx]), PATH)
            except:
                pass


    def scrape(self, search, min_image_count, directory):
        self._initiateSession(key=search)
        self._acceptCookies()

        totalImageCount = 0
        while totalImageCount < min_image_count:
            urlList = self._deduplicate(self._getURL())
            self._downloader(data=urlList,
                             key=search,
                             out_dir=directory)
            urlList.clear()
            totalImageCount = self._totalImages(os.path.join(os.getcwd(), search.replace(" ", "_")))
            print("current Image count: {}".format(totalImageCount))
            self._endOfPage()
            time.sleep(2)

        if totalImageCount >= min_image_count:
            self.wd.quit()


class EBirdScraperBot():
    def __init__(self, headless=False):
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # Gunakan webdriver-manager untuk otomatis download ChromeDriver
        try:
            service = Service(ChromeDriverManager().install())
            self.wd = webdriver.Chrome(service=service, options=chrome_options)
        except Exception as e:
            print(f"Error initializing Chrome driver: {e}")
            # Fallback ke cara lama jika webdriver-manager gagal
            try:
                self.wd = webdriver.Chrome(executable_path=os.path.join(os.getcwd(), 'chromedriver.exe'), options=chrome_options)
            except:
                self.wd = webdriver.Chrome(options=chrome_options)

        # Disable webdriver detection
        self.wd.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        self.wait = WebDriverWait(self.wd, 10)
        self.base_url = "https://media.ebird.org"

    def _scroll_and_load_more(self):
        """Scroll ke bawah untuk memuat lebih banyak gambar"""
        last_height = self.wd.execute_script("return document.body.scrollHeight")

        while True:
            # Scroll ke bawah
            self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)

            # Tunggu untuk loading
            new_height = self.wd.execute_script("return document.body.scrollHeight")
            if new_height == last_height:
                break
            last_height = new_height

    def _get_clickable_images_from_page(self):
        """Cari semua gambar yang bisa diklik untuk melihat versi penuh"""
        clickable_images = []

        try:
            # Tunggu sampai halaman dimuat
            time.sleep(3)

            # Cari elemen gambar yang bisa diklik (biasanya dalam link atau card)
            selectors = [
                "a[href*='/catalog/'] img",  # Gambar dalam link catalog
                ".MediaCard img",            # Gambar dalam MediaCard
                ".MediaThumbnail img",       # Gambar thumbnail
                "[data-testid='media-card'] img",  # Gambar dalam media card
                "img[src*='macaulaylibrary.org']",  # Gambar langsung
                "img[src*='cdn.download.ams.birds.cornell.edu']"  # Gambar CDN
            ]

            for selector in selectors:
                try:
                    images = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    print(f"Found {len(images)} clickable images with selector: {selector}")

                    for img in images:
                        # Cek apakah gambar bisa diklik (ada parent link atau clickable)
                        parent_link = None
                        try:
                            # Cari parent link
                            parent_link = img.find_element(By.XPATH, "./ancestor::a[1]")
                        except:
                            # Jika tidak ada parent link, coba klik gambar langsung
                            pass

                        clickable_images.append({
                            'element': img,
                            'parent_link': parent_link,
                            'src': img.get_attribute('src') or img.get_attribute('data-src')
                        })

                except Exception as e:
                    print(f"Error with selector {selector}: {e}")
                    continue

            print(f"Total clickable images found: {len(clickable_images)}")
            return clickable_images

        except Exception as e:
            print(f"Error finding clickable images: {e}")
            return []

    def _click_and_get_full_image(self, clickable_img, index):
        """Klik gambar untuk melihat versi penuh, kemudian download atau screenshot"""
        try:
            print(f"Processing image {index + 1}...")

            # Scroll ke elemen agar terlihat
            self.wd.execute_script("arguments[0].scrollIntoView(true);", clickable_img['element'])
            time.sleep(1)

            # Klik gambar atau parent link
            if clickable_img['parent_link']:
                print(f"Clicking parent link for image {index + 1}")
                clickable_img['parent_link'].click()
            else:
                print(f"Clicking image {index + 1} directly")
                clickable_img['element'].click()

            time.sleep(3)  # Tunggu halaman/modal dimuat

            # Cari gambar resolusi penuh
            full_image_url = self._find_full_resolution_image()

            if full_image_url:
                print(f"Found full resolution image: {full_image_url[:100]}...")
                return full_image_url
            else:
                # Jika tidak menemukan gambar penuh, ambil screenshot
                print(f"Taking screenshot for image {index + 1}")
                return self._take_screenshot(index)

        except Exception as e:
            print(f"Error clicking image {index + 1}: {e}")
            return None
        finally:
            # Kembali ke halaman utama jika membuka tab/window baru
            self._return_to_main_page()

    def _find_full_resolution_image(self):
        """Cari gambar resolusi penuh setelah klik"""
        try:
            # Berbagai selector untuk gambar resolusi penuh
            full_image_selectors = [
                "img[src*='macaulaylibrary.org']:not([src*='thumbnail'])",
                "img[src*='cdn.download.ams.birds.cornell.edu']:not([src*='thumbnail'])",
                ".MediaDisplay img",
                ".MediaViewer img",
                ".FullscreenImage img",
                "[data-testid='media-display'] img",
                ".modal img",
                ".lightbox img",
                "img[alt*='photo']",
                "img[style*='max-width']"  # Gambar dengan style max-width biasanya full size
            ]

            for selector in full_image_selectors:
                try:
                    full_img = self.wd.find_element(By.CSS_SELECTOR, selector)
                    img_url = full_img.get_attribute('src')

                    if img_url and any(domain in img_url for domain in ['macaulaylibrary.org', 'cdn.download.ams.birds.cornell.edu']):
                        # Pastikan ini bukan thumbnail
                        if 'thumbnail' not in img_url.lower() and 'thumb' not in img_url.lower():
                            return self._convert_to_high_res(img_url)

                except:
                    continue

            return None

        except Exception as e:
            print(f"Error finding full resolution image: {e}")
            return None

    def _take_screenshot(self, index):
        """Ambil screenshot jika tidak bisa mendapatkan URL gambar"""
        try:
            # Cari area gambar untuk screenshot
            screenshot_selectors = [
                ".MediaDisplay",
                ".MediaViewer",
                ".FullscreenImage",
                "[data-testid='media-display']",
                ".modal-content",
                ".lightbox-content"
            ]

            screenshot_element = None
            for selector in screenshot_selectors:
                try:
                    screenshot_element = self.wd.find_element(By.CSS_SELECTOR, selector)
                    break
                except:
                    continue

            if screenshot_element:
                # Screenshot elemen spesifik
                screenshot_path = f"ebird_screenshot_{index + 1:03d}.png"
                screenshot_element.screenshot(screenshot_path)
                print(f"Screenshot saved: {screenshot_path}")
                return screenshot_path
            else:
                # Screenshot seluruh halaman
                screenshot_path = f"ebird_fullpage_{index + 1:03d}.png"
                self.wd.save_screenshot(screenshot_path)
                print(f"Full page screenshot saved: {screenshot_path}")
                return screenshot_path

        except Exception as e:
            print(f"Error taking screenshot: {e}")
            return None

    def _return_to_main_page(self):
        """Kembali ke halaman utama"""
        try:
            # Jika ada multiple windows/tabs, tutup yang baru dan kembali ke utama
            if len(self.wd.window_handles) > 1:
                self.wd.close()
                self.wd.switch_to.window(self.wd.window_handles[0])
            else:
                # Jika modal/overlay, coba tutup dengan ESC atau tombol close
                try:
                    # Coba tekan ESC
                    from selenium.webdriver.common.keys import Keys
                    self.wd.find_element(By.TAG_NAME, 'body').send_keys(Keys.ESCAPE)
                    time.sleep(1)
                except:
                    pass

                # Coba cari tombol close
                close_selectors = [
                    ".close", ".modal-close", ".lightbox-close",
                    "[aria-label='Close']", "[title='Close']",
                    ".fa-times", ".fa-close", ".icon-close"
                ]

                for selector in close_selectors:
                    try:
                        close_btn = self.wd.find_element(By.CSS_SELECTOR, selector)
                        close_btn.click()
                        time.sleep(1)
                        break
                    except:
                        continue

        except Exception as e:
            print(f"Error returning to main page: {e}")

    def _get_image_urls_from_page(self):
        """Ekstrak URL gambar dengan metode klik dan lihat (DEPRECATED - gunakan _click_and_download_images)"""
        # Method lama untuk backward compatibility
        return self._click_and_download_images(max_images=50, download_method='url_only')

    def _convert_to_high_res(self, img_url):
        """Konversi URL gambar ke resolusi tinggi"""
        if not img_url:
            return None

        try:
            # Berbagai pola untuk konversi ke resolusi tinggi
            if 'macaulaylibrary.org' in img_url:
                # Ganti parameter size jika ada
                if '?size=' in img_url:
                    img_url = img_url.split('?size=')[0]

                # Tambahkan parameter untuk resolusi tinggi
                if '?' in img_url:
                    img_url += '&size=large'
                else:
                    img_url += '?size=large'

            elif 'cdn.download.ams.birds.cornell.edu' in img_url:
                # Untuk CDN Cornell, coba ganti ukuran
                img_url = img_url.replace('/thumbnails/', '/catalog/')
                img_url = img_url.replace('_thumbnail', '')
                img_url = img_url.replace('_small', '')
                img_url = img_url.replace('_medium', '')

            return img_url

        except Exception as e:
            print(f"Error converting URL to high res: {e}")
            return img_url

    def _get_high_res_image_from_detail(self, detail_url):
        """Ambil URL gambar resolusi tinggi dari halaman detail"""
        try:
            # Buka tab baru untuk halaman detail
            self.wd.execute_script("window.open('');")
            self.wd.switch_to.window(self.wd.window_handles[1])

            self.wd.get(detail_url)
            time.sleep(3)

            # Cari gambar resolusi tinggi dengan berbagai selector
            selectors = [
                "img[src*='macaulaylibrary.org']:not([src*='thumbnail'])",
                "img[src*='cdn.download.ams.birds.cornell.edu']:not([src*='thumbnail'])",
                ".MediaDisplay img",
                ".MediaViewer img",
                "[data-testid='media-display'] img",
                "img[alt*='photo']",
                "img[alt*='image']"
            ]

            img_url = None
            for selector in selectors:
                try:
                    high_res_img = self.wd.find_element(By.CSS_SELECTOR, selector)
                    img_url = high_res_img.get_attribute('src')
                    if img_url and any(domain in img_url for domain in ['macaulaylibrary.org', 'cdn.download.ams.birds.cornell.edu']):
                        break
                except:
                    continue

            # Tutup tab dan kembali ke tab utama
            self.wd.close()
            self.wd.switch_to.window(self.wd.window_handles[0])

            return self._convert_to_high_res(img_url) if img_url else None

        except Exception as e:
            print(f"Error getting high res image from {detail_url}: {e}")
            # Jika ada error, tutup tab dan kembali ke tab utama
            try:
                if len(self.wd.window_handles) > 1:
                    self.wd.close()
                    self.wd.switch_to.window(self.wd.window_handles[0])
            except:
                pass
            return None

    def _download_image(self, img_url, filename, output_dir):
        """Download gambar dari URL"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(img_url, headers=headers, stream=True)
            response.raise_for_status()

            filepath = os.path.join(output_dir, filename)

            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            return True

        except Exception as e:
            print(f"Error downloading {img_url}: {e}")
            return False

    def _click_and_download_images(self, max_images=50, download_method='both'):
        """Method utama: klik gambar, lihat versi penuh, kemudian download/screenshot"""
        downloaded_items = []

        try:
            # Cari semua gambar yang bisa diklik
            clickable_images = self._get_clickable_images_from_page()

            if not clickable_images:
                print("No clickable images found")
                return []

            # Batasi jumlah gambar
            if max_images and len(clickable_images) > max_images:
                clickable_images = clickable_images[:max_images]

            print(f"Processing {len(clickable_images)} images...")

            for i, clickable_img in enumerate(tqdm.tqdm(clickable_images, desc="Processing images")):
                try:
                    # Klik dan dapatkan gambar penuh
                    result = self._click_and_get_full_image(clickable_img, i)

                    if result:
                        downloaded_items.append(result)

                    # Jeda untuk menghindari rate limiting
                    time.sleep(1)

                except Exception as e:
                    print(f"Error processing image {i+1}: {e}")
                    continue

            return downloaded_items

        except Exception as e:
            print(f"Error in click and download process: {e}")
            return []

    def scrape_ebird(self, ebird_url, output_dir, max_images=50, method='click_and_view'):
        """Scrape foto dari eBird dengan berbagai metode"""
        print(f"Memulai scraping dari: {ebird_url}")
        print(f"Method: {method}")

        # Buat direktori output
        os.makedirs(output_dir, exist_ok=True)

        # Buka halaman eBird
        self.wd.get(ebird_url)
        time.sleep(3)

        # Scroll dan muat lebih banyak gambar
        print("Memuat gambar...")
        self._scroll_and_load_more()

        downloaded_count = 0

        if method == 'click_and_view':
            # Method baru: klik gambar, lihat versi penuh, kemudian download
            print("Menggunakan metode klik dan lihat...")

            # Cari gambar yang bisa diklik
            clickable_images = self._get_clickable_images_from_page()

            if not clickable_images:
                print("Tidak ada gambar yang bisa diklik, menggunakan metode lama...")
                method = 'direct_url'
            else:
                # Batasi jumlah gambar
                if max_images and len(clickable_images) > max_images:
                    clickable_images = clickable_images[:max_images]

                print(f"Memproses {len(clickable_images)} gambar...")

                for i, clickable_img in enumerate(tqdm.tqdm(clickable_images, desc="Processing")):
                    try:
                        # Klik dan dapatkan gambar penuh
                        result = self._click_and_get_full_image(clickable_img, i)

                        if result:
                            if result.endswith('.png'):
                                # Ini adalah screenshot
                                # Pindahkan ke output directory
                                import shutil
                                new_path = os.path.join(output_dir, os.path.basename(result))
                                shutil.move(result, new_path)
                                downloaded_count += 1
                            else:
                                # Ini adalah URL gambar, download
                                filename = f"ebird_image_{i+1:03d}.jpg"
                                if self._download_image(result, filename, output_dir):
                                    downloaded_count += 1

                        # Jeda untuk menghindari rate limiting
                        time.sleep(1)

                    except Exception as e:
                        print(f"Error processing image {i+1}: {e}")
                        continue

        if method == 'direct_url' or downloaded_count == 0:
            # Method lama: ambil URL langsung
            print("Menggunakan metode URL langsung...")
            image_urls = self._get_image_urls_old_method()

            print(f"Ditemukan {len(image_urls)} gambar")

            # Batasi jumlah gambar jika diperlukan
            if max_images and len(image_urls) > max_images:
                image_urls = image_urls[:max_images]

            # Download gambar
            for i, img_url in enumerate(tqdm.tqdm(image_urls, desc="Downloading")):
                filename = f"ebird_image_{i+1:03d}.jpg"

                if self._download_image(img_url, filename, output_dir):
                    downloaded_count += 1

                time.sleep(0.5)  # Jeda untuk menghindari rate limiting

        print(f"Berhasil mendownload {downloaded_count} gambar ke {output_dir}")

        self.wd.quit()
        return downloaded_count

    def _get_image_urls_old_method(self):
        """Method lama untuk mengambil URL gambar langsung"""
        image_urls = []

        try:
            # Cari semua elemen gambar dengan berbagai selector
            selectors = [
                "img[src*='macaulaylibrary.org']",
                "img[src*='cdn.download.ams.birds.cornell.edu']",
                "img[data-src*='macaulaylibrary.org']",
                "img[data-src*='cdn.download.ams.birds.cornell.edu']",
                ".MediaCard img",
                ".MediaThumbnail img",
                "[data-testid='media-card'] img"
            ]

            for selector in selectors:
                try:
                    images = self.wd.find_elements(By.CSS_SELECTOR, selector)

                    for img in images:
                        # Coba ambil dari src atau data-src
                        img_url = img.get_attribute('src') or img.get_attribute('data-src')

                        if img_url and any(domain in img_url for domain in ['macaulaylibrary.org', 'cdn.download.ams.birds.cornell.edu']):
                            # Konversi ke URL resolusi tinggi
                            high_res_url = self._convert_to_high_res(img_url)
                            if high_res_url:
                                image_urls.append(high_res_url)

                except Exception as e:
                    continue

            # Hapus duplikat
            unique_urls = list(set(image_urls))
            return unique_urls

        except Exception as e:
            print(f"Error getting image URLs: {e}")
            return []


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Web scraper for Google Images and eBird photos")

    # Tambahkan pilihan mode
    parser.add_argument('--mode', choices=['google', 'ebird'], default='google',
                        help='Pilih mode scraping: google untuk Google Images, ebird untuk eBird')

    # Argumen untuk Google Images (mode lama)
    parser.add_argument('--search', type=str,
                        help='The keyword to search in Google Images')

    parser.add_argument('--min_image_count', type=int, default=1,
                        help='Minimum number of images for Google Images scraping')

    # Argumen untuk eBird
    parser.add_argument('--ebird_url', type=str,
                        help='URL eBird untuk scraping foto (contoh: https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1)')

    parser.add_argument('--max_images', type=int, default=50,
                        help='Maksimum jumlah gambar untuk didownload dari eBird')

    parser.add_argument('--method', choices=['click_and_view', 'direct_url'], default='click_and_view',
                        help='Metode scraping: click_and_view (klik gambar dulu, lihat versi penuh) atau direct_url (ambil URL langsung)')

    # Argumen umum
    parser.add_argument('--out_directory', default=os.getcwd(),
                        help='The full path to the output directory')

    parser.add_argument('--headless', action='store_true',
                        help='Jalankan browser dalam mode headless (tanpa GUI)')

    args = parser.parse_args()

    if args.mode == 'google':
        if not args.search:
            print("Error: --search diperlukan untuk mode Google Images")
            exit(1)

        SEARCH = args.search
        IMAGECOUNT = args.min_image_count
        OUTDIR = args.out_directory

        scraperBot = ScraperBot()
        scraperBot.scrape(search=SEARCH, min_image_count=IMAGECOUNT, directory=OUTDIR)

    elif args.mode == 'ebird':
        if not args.ebird_url:
            print("Error: --ebird_url diperlukan untuk mode eBird")
            print("Contoh: python scraperBot.py --mode ebird --ebird_url 'https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1'")
            exit(1)

        ebird_scraper = EBirdScraperBot(headless=args.headless)
        ebird_scraper.scrape_ebird(
            ebird_url=args.ebird_url,
            output_dir=args.out_directory,
            max_images=args.max_images,
            method=args.method
        )