#!/usr/bin/env python3
"""
Enhanced eBird Scraper Test Script

This script demonstrates the new enhanced features:
1. Screenshot-based image capture with overlay handling
2. Automatic "Load More" detection and handling
3. Bulk processing with progress feedback
4. Robust error handling and retry mechanisms
5. Comprehensive statistics and reporting
"""

import os
import sys
import time
from scraperBot import EBirdScraperBot

def test_enhanced_features():
    """Test all enhanced features of the eBird scraper"""
    print("🧪 TESTING ENHANCED EBIRD SCRAPER FEATURES")
    print("=" * 60)
    
    # Test configuration
    test_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
    test_dir = os.path.join(os.getcwd(), "enhanced_test_output")
    
    print(f"📍 Test URL: {test_url}")
    print(f"📁 Output directory: {test_dir}")
    print(f"🎯 Testing with 5 images for demonstration")
    print("=" * 60)
    
    try:
        # Test 1: Enhanced Click and View Method
        print("\n🧪 TEST 1: Enhanced Click and View Method")
        print("-" * 40)
        
        scraper1 = EBirdScraperBot(headless=False)  # GUI mode to see the process
        
        start_time = time.time()
        result_count = scraper1.scrape_ebird(
            ebird_url=test_url,
            output_dir=os.path.join(test_dir, "click_and_view"),
            max_images=5,
            method='click_and_view',
            timeout_minutes=10  # 10 minute timeout for test
        )
        
        test1_time = time.time() - start_time
        print(f"\n✅ Test 1 completed in {test1_time:.1f} seconds")
        print(f"📊 Result: {result_count} items processed")
        
        # Verify files were created
        output_dir1 = os.path.join(test_dir, "click_and_view")
        if os.path.exists(output_dir1):
            files = [f for f in os.listdir(output_dir1) if f.endswith(('.jpg', '.png'))]
            print(f"📁 Files created: {len(files)}")
            for file in files[:3]:  # Show first 3 files
                print(f"  - {file}")
            if len(files) > 3:
                print(f"  ... and {len(files) - 3} more files")
        
    except Exception as e:
        print(f"❌ Test 1 failed: {e}")
    
    try:
        # Test 2: Direct URL Method (Fallback)
        print("\n🧪 TEST 2: Direct URL Method (Fallback)")
        print("-" * 40)
        
        scraper2 = EBirdScraperBot(headless=True)  # Headless for speed
        
        start_time = time.time()
        result_count2 = scraper2.scrape_ebird(
            ebird_url=test_url,
            output_dir=os.path.join(test_dir, "direct_url"),
            max_images=5,
            method='direct_url',
            timeout_minutes=5  # 5 minute timeout for test
        )
        
        test2_time = time.time() - start_time
        print(f"\n✅ Test 2 completed in {test2_time:.1f} seconds")
        print(f"📊 Result: {result_count2} items processed")
        
        # Verify files were created
        output_dir2 = os.path.join(test_dir, "direct_url")
        if os.path.exists(output_dir2):
            files = [f for f in os.listdir(output_dir2) if f.endswith(('.jpg', '.png'))]
            print(f"📁 Files created: {len(files)}")
            for file in files[:3]:  # Show first 3 files
                print(f"  - {file}")
            if len(files) > 3:
                print(f"  ... and {len(files) - 3} more files")
        
    except Exception as e:
        print(f"❌ Test 2 failed: {e}")
    
    # Final summary
    print("\n" + "=" * 60)
    print("🏁 ENHANCED FEATURES TEST SUMMARY")
    print("=" * 60)
    print("✅ Features tested:")
    print("  🖱️ Enhanced click and view with retry logic")
    print("  📸 High-quality screenshot capture with overlay handling")
    print("  📜 Automatic 'Load More' button detection and clicking")
    print("  📊 Comprehensive progress feedback and statistics")
    print("  ⚠️ Robust error handling and recovery")
    print("  ⏱️ Configurable timeout protection")
    print("  🔄 Bulk processing with rate limiting")
    
    print(f"\n📁 All test files saved to: {test_dir}")
    print("\n🎉 Enhanced eBird scraper is ready for production use!")
    
    return True

def demonstrate_command_line_usage():
    """Show examples of how to use the enhanced scraper from command line"""
    print("\n" + "=" * 60)
    print("💻 COMMAND LINE USAGE EXAMPLES")
    print("=" * 60)
    
    examples = [
        {
            "title": "Basic Enhanced Scraping",
            "command": "python scraperBot.py --mode ebird --ebird_url \"https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1\" --max_images 20"
        },
        {
            "title": "With Custom Output Directory",
            "command": "python scraperBot.py --mode ebird --ebird_url \"YOUR_EBIRD_URL\" --out_directory \"./bird_photos\" --max_images 15"
        },
        {
            "title": "Headless Mode with Timeout",
            "command": "python scraperBot.py --mode ebird --ebird_url \"YOUR_EBIRD_URL\" --headless --timeout 45 --max_images 50"
        },
        {
            "title": "Direct URL Method (Fast)",
            "command": "python scraperBot.py --mode ebird --ebird_url \"YOUR_EBIRD_URL\" --method direct_url --max_images 30"
        },
        {
            "title": "Maximum Quality (Click and View)",
            "command": "python scraperBot.py --mode ebird --ebird_url \"YOUR_EBIRD_URL\" --method click_and_view --timeout 60 --max_images 100"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['title']}:")
        print(f"   {example['command']}")
    
    print("\n📋 New Parameters:")
    print("  --timeout N      : Set timeout in minutes (default: 30)")
    print("  --method METHOD  : Choose 'click_and_view' or 'direct_url'")
    print("  --headless       : Run without browser GUI")
    print("  --max_images N   : Limit number of images to process")

if __name__ == "__main__":
    print("🚀 Starting Enhanced eBird Scraper Tests...")
    
    # Run the comprehensive test
    success = test_enhanced_features()
    
    # Show command line examples
    demonstrate_command_line_usage()
    
    if success:
        print("\n🎊 All tests completed successfully!")
        print("The enhanced eBird scraper is ready for use.")
    else:
        print("\n⚠️ Some tests encountered issues.")
        print("Please check the error messages above.")
