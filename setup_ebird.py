#!/usr/bin/env python3
"""
Setup script untuk eBird Scraper Bo<PERSON>
Script ini akan menginstall dependencies dan setup ChromeDriver otomatis
"""

import subprocess
import sys
import os

def install_requirements():
    """Install packages dari requirements.txt"""
    print("Installing Python packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Python packages installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Error installing packages: {e}")
        return False

def setup_chromedriver():
    """Setup ChromeDriver menggunakan webdriver-manager"""
    print("Setting up ChromeDriver...")
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        
        # Download dan setup ChromeDriver
        driver_path = ChromeDriverManager().install()
        print(f"✓ ChromeDriver installed at: {driver_path}")
        
        # Test ChromeDriver
        service = Service(driver_path)
        options = webdriver.ChromeOptions()
        options.add_argument("--headless")
        
        driver = webdriver.Chrome(service=service, options=options)
        driver.get("https://www.google.com")
        driver.quit()
        
        print("✓ ChromeDriver test successful")
        return True
        
    except Exception as e:
        print(f"✗ Error setting up ChromeDriver: {e}")
        return False

def test_ebird_scraper():
    """Test eBird scraper dengan contoh kecil"""
    print("Testing eBird scraper...")
    try:
        from scraperBot import EBirdScraperBot
        
        # Test dengan mode headless dan hanya 1 gambar
        scraper = EBirdScraperBot(headless=True)
        
        test_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
        test_dir = os.path.join(os.getcwd(), "test_ebird")
        
        downloaded = scraper.scrape_ebird(
            ebird_url=test_url,
            output_dir=test_dir,
            max_images=1
        )
        
        if downloaded > 0:
            print(f"✓ eBird scraper test successful - downloaded {downloaded} image(s)")
            return True
        else:
            print("✗ eBird scraper test failed - no images downloaded")
            return False
            
    except Exception as e:
        print(f"✗ Error testing eBird scraper: {e}")
        return False

def main():
    print("=== eBird Scraper Bot Setup ===\n")
    
    success = True
    
    # Step 1: Install requirements
    if not install_requirements():
        success = False
    
    print()
    
    # Step 2: Setup ChromeDriver
    if not setup_chromedriver():
        success = False
    
    print()
    
    # Step 3: Test scraper
    if success:
        if not test_ebird_scraper():
            success = False
    
    print("\n=== Setup Summary ===")
    if success:
        print("✓ Setup completed successfully!")
        print("\nYou can now use the eBird scraper:")
        print("python scraperBot.py --mode ebird --ebird_url 'YOUR_EBIRD_URL' --max_images 10")
        print("\nOr run the example:")
        print("python ebird_example.py")
    else:
        print("✗ Setup failed. Please check the errors above.")
        print("\nTry manual installation:")
        print("1. pip install -r requirements.txt")
        print("2. Download ChromeDriver manually from https://chromedriver.chromium.org/")

if __name__ == "__main__":
    main()
