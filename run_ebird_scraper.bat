@echo off
echo =======================================
echo       eBird Photo Scraper Bot
echo =======================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo Python is installed.
echo.

REM Check if requirements are installed
echo Checking dependencies...
python -c "import selenium, requests, tqdm" >nul 2>&1
if errorlevel 1 (
    echo Installing required packages...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo Error: Failed to install requirements
        pause
        exit /b 1
    )
)

echo Dependencies are ready.
echo.

REM Show usage examples
echo Usage Examples:
echo.
echo 1. Download Javan Munia photos from Indonesia:
echo    python scraperBot.py --mode ebird --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" --max_images 20
echo.
echo 2. Download with custom output directory:
echo    python scraperBot.py --mode ebird --ebird_url "YOUR_EBIRD_URL" --out_directory "./bird_photos" --max_images 15
echo.
echo 3. Run in headless mode (no browser window):
echo    python scraperBot.py --mode ebird --ebird_url "YOUR_EBIRD_URL" --headless --max_images 10
echo.

REM Interactive mode
echo =======================================
echo       Interactive Mode
echo =======================================
echo.

set /p "ebird_url=Enter eBird URL: "
if "%ebird_url%"=="" (
    echo Using default URL for Javan Munia in Indonesia...
    set "ebird_url=https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
)

set /p "max_images=Enter max images to download (default 20): "
if "%max_images%"=="" set "max_images=20"

set /p "output_dir=Enter output directory (default current): "
if "%output_dir%"=="" set "output_dir=."

set /p "headless=Run in headless mode? (y/n, default n): "
if /i "%headless%"=="y" (
    set "headless_flag=--headless"
) else (
    set "headless_flag="
)

echo.
echo Starting eBird scraper with:
echo URL: %ebird_url%
echo Max images: %max_images%
echo Output directory: %output_dir%
echo Headless: %headless%
echo.

REM Run the scraper
python scraperBot.py --mode ebird --ebird_url "%ebird_url%" --max_images %max_images% --out_directory "%output_dir%" %headless_flag%

if errorlevel 1 (
    echo.
    echo Error occurred during scraping.
    echo Try running the test script: python test_ebird.py
) else (
    echo.
    echo Scraping completed successfully!
)

echo.
pause
